import axios from "axios";
import { auth, db, storage } from "@/firebase.config";
import {
  collection,
  addDoc,
  doc,
  getDoc,
  updateDoc,
  arrayUnion,
  query,
  where,
  getDocs,
  Timestamp,
  setDoc,
  writeBatch,
} from "firebase/firestore";
import {
  deleteObject,
  getBlob,
  getDownloadURL,
  getStorage,
  ref,
  uploadBytes,
} from "firebase/storage";
import { res } from "@/components/inspectionMetaData/newTempResponse";
import { fetch as axiosFetch } from "../libs/helpers";
import { v4 as uuidv4 } from "uuid";

const host = process.env.NEXT_HOST;

export const addInspectionDetail = async (
  inspectionDetails: any,
  inspectionType: any,
  user_id: any,
  projectId: any
) => {
  try {
    const projectRef = doc(db, "projects", projectId);
    const reporttypeRef = doc(db, "report_type", inspectionType);
    const UserRef = doc(db, "user", user_id);
    inspectionDetails.reporttype = reporttypeRef;
    inspectionDetails.user = UserRef;
    inspectionDetails.project = projectRef;
    inspectionDetails.projectList = [projectRef];

    const docRef = await addDoc(
      collection(db, "inspection"),
      inspectionDetails
    );
    if (docRef.id) {
      await updateDoc(projectRef, {
        inspections: arrayUnion(docRef),
      });
      return {
        type: true,
        data: docRef,
      };
    } else {
      return {
        type: false,
      };
    }
  } catch (e) {
    return {
      type: false,
    };
  }
};

export const fetchInspection = async (inspectionId: any) => {
  try {
    const docRef = doc(db, "inspection", inspectionId);

    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      const doc = await docSnap.data();
      return doc;
    } else {
      return false;
    }
  } catch (error) {
    return false;
  }
};

export const updateInspectionDetails = async (
  inspectionDetails: any,
  inspectionId: string,
) => {
  try {
    const docRef = doc(db, "inspection", inspectionId);
    await updateDoc(docRef, inspectionDetails);
    return true;
  } catch (e) {
    return false;
  }
};

export const checkUniqueFields = async (uniqueFields: any[]) => {

  const clientId = localStorage.getItem("client");
  // const resultObject: { [key: string]: boolean } = {}; // Object to store results

  for (const field of uniqueFields) {
    if (!field.suggested_value) {
      // resultObject[field.id] = false; // Default to false if no suggested_value
      continue;
    }

    const inspectionsRef = collection(db, "inspection");
    const q = query(inspectionsRef, where("client", "==", clientId), where("unique_key", "==", field.suggested_value?.toLowerCase()));

    const querySnapshot = await getDocs(q);

    // Store true if a document matches, else false
    // resultObject[field.name] = !querySnapshot.empty;
    
    if (!querySnapshot.empty) {
      return {
        isUnique: false,
        fieldId: field.id,
        fieldName: field.name,
        fieldValue: field.suggested_value,
      }
    }
  }

  return {
    isUnique: true,
    fieldId: null,
    fieldName: null,
    fieldValue: null,
  };
  // return resultObject;
}

// export const generateTypeFields = async (inspection_type_id: any) => {
//   const idToken = localStorage.getItem("ScalarIdToken");
//   const projectId = localStorage.getItem("ScalarProjectId")
//   const response: any = await axios.get(
//     `${host}/v3/inspection_types/${inspection_type_id}/projects/${projectId}/inspections/template`,
//     {
//       headers: {
//         Authorization: `Bearer ${idToken}`,
//       }
//     }
//   );
//   return response;
//   // return res;
// };

export const generateTypeFields = async (
  inspection_type_id: any,
  project_id: any
): Promise<any> => {
  const idToken = localStorage.getItem("ScalarIdToken");
  const res = await axiosFetch({
    url: `${host}/v4/inspection_types/${inspection_type_id}/projects/${project_id}/inspections/template`,
    method: "GET",
  });
  return res;
};

const tempOptionFields = {
  "suggestions": {
    "fields": [
      {
        "id": "element",
        "name": "Element",
        "options": [
          { "id": "A4.23", "value": "Vloeren" },
          { "id": "A4.24", "value": "Trappen en hellingen" },
          { "id": "A4.34", "value": "Balustrades en leuningen" }
        ],
        "recalculation_parents": [],
        "required": true,
        "suggested_value": "A4.23",
        "type": "picker",
        "conditional_field": false
      },
      {
        "id": "bouwdeel",
        "name": "Bouwdeel",
        "options": {
          "A4.23": [
            { "id": "A4.23.10", "value": "Onderconstructies" },
            { "id": "A4.23.20", "value": "Bordessen" }
          ],
          "A4.24": [
            { "id": "A4.24.10", "value": "Trappen" },
            { "id": "A4.24.20", "value": "Hellingen" },
            { "id": "A4.24.30", "value": "Ladders en klimijzers" }
          ],
          "A4.34": [
            { "id": "A4.34.10", "value": "Constructief algemeen" },
            { "id": "A4.34.20", "value": "Leuningen" }
          ]
        },
        "parent_field": "element",
        "recalculation_parents": [],
        "required": true,
        "suggested_value": null,
        "type": "picker",
        "conditional_field": true,
        "conditional_parent_field_id": "A4.34"
      }
    ],
    "finding_type": ""
  }
};

export const generateSuggestionFieldsFields = async (
  inspectionType: string,
  findingTypeId: string
): Promise<any> => {
  const res = await axiosFetch({
    url: `${host}/inspection_types/${inspectionType}/findings/${findingTypeId}/template`,
    method: "POST",
    data: {
      image_urls: [],
    }
  });
  return res;
};

export const handleUpload = async (file: File, folderName: string) => {
  const storage = getStorage();
  // Generate timestamp and create new filename
  const timestamp = Date.now();
  const fileExtension = file.name.split(".").pop();
  const newFileName = `image_${timestamp}.${fileExtension}`;
  const storageRef = ref(storage, `uploads/${folderName}/${newFileName}`);

  try {
    const snapshot = await uploadBytes(storageRef, file);
    const downloadURL = await getDownloadURL(snapshot.ref);

    return downloadURL;
  } catch (error) {
    throw error;
  }
};

const getFilePathFromUrl = (url: string) => {
  try {
    // Handle null or undefined URLs
    if (!url) return null;

    const urlObj = new URL(url);
    const path = urlObj.pathname.split("/o/")[1];
    if (!path) return null;

    return decodeURIComponent(path);
  } catch (error) {
    console.error("Error parsing URL:", url, error);
    return null;
  }
};

export const deleteFile = async (url: string) => {
  try {
    const filePath = getFilePathFromUrl(url);
    if (filePath) {
      const fileRef = ref(storage, filePath);
      await deleteObject(fileRef);
    } else {
      console.warn(`Invalid file path for URL: ${url}`);
      return false;
    }
  } catch (error) {
    console.error("Error in deleteFile:", error);
  }
};

// function to fetch all finding of a inspection
export const fetchAllFindings = async (userId: any, inspectionId: any) => {
  try {
    const inspectionRef = doc(db, "inspection", inspectionId);
    const collectionRef = collection(db, "finding");
    const clientId = localStorage.getItem("client");

    const condition = query(
      collectionRef,
      where("inspection", "==", inspectionRef),
      where("client", "==", clientId)
    );

    const querySnapshot = await getDocs(condition);

    const documents = querySnapshot.docs.map((doc: any) => ({
      id: doc.id,
      ...doc.data(),
    }));
    return documents;
  } catch (error) {
    return false;
  }
};

export const addFindingsForInspection = async (
  grouped: any,
  inspectionId: string
) => {
  const orderArray: any = []
  const findingOrder: any = []
  const inspectionRef = doc(db, "inspection", inspectionId);
  const findingWithNewIds: any = [];
  const batch = writeBatch(db);

  for (let i = 0; i < grouped.length; i++) {
    const item = grouped[i];
    const decompRef = doc(collection(db, "finding"));
    const decomp = item.decompositionItem;
    decomp.id = decompRef.id;
    decomp.inspection = inspectionRef;
    findingWithNewIds.push(decomp);
    // orderArray.push(decompRef)

    const allFindings = item.findings;
    allFindings.forEach((item: any) => {
      const finding = item.finding;
      const findRef = doc(collection(db, "finding"));
      finding.id = findRef.id;
      finding.parent_finding_id = decompRef.id;
      finding.inspection = inspectionRef;
      findingWithNewIds.push(finding);
      // orderArray.push(findRef)

      const measure = item.measure;
      measure.forEach((item: any) => {
        const measureRef = doc(collection(db, "finding"));
        item.id = measureRef.id;
        item.parent_finding_id = findRef.id;
        item.inspection = inspectionRef;
        findingWithNewIds.push(item); // Corrected to push the measure item
        // orderArray.push(measureRef)
      });
    });
  }

  const findingsWithoutChildren = findingWithNewIds.map(
    ({ children, ...rest }: any) => rest
  );

  // Process all findings sequentially with Promise.all
  try {
    // Process all media uploads first
    const processedFindings = await Promise.all(
      findingsWithoutChildren.map(async (finding: any) => {
        const { id, ...rest } = finding;
        if (
          rest.finding_type.id === "C0SN2NEFQCbWe71I22nU" ||
          rest.finding_type.id === "measure"
        ) {
          return {
            id,
            data: {
              ...rest,
              created_at: Timestamp.now(),
              updated_at: Timestamp.now(),
            },
          };
        } else {
          return {
            id,
            data: {
              ...rest,
              created_at: Timestamp.now(),
              updated_at: Timestamp.now(),
            },
          };
        }
      })
    );

    // Add all processed findings to the batch
    processedFindings.forEach(({ id, data }) => {
      const docRef = doc(db, "finding", id);
      findingOrder.push(id)
      orderArray.push(docRef)
      batch.set(docRef, data);
    });

    localStorage.setItem("findingOrder", JSON.stringify(findingOrder))

    // Commit the batch
    await batch.commit();
    const inspectionDetails = {
      finding_order: orderArray
    }
    await updateInspectionDetails(inspectionDetails, inspectionId)
    return true;
  } catch (error) {
    console.error("Error adding findings:", error);
    return false;
  }
};

const generateNewMedia = async (media: any[]) => {
  try {
    if (!media || !Array.isArray(media)) return [];

    const newMediaArray = await Promise.all(
      media.map(async (item) => {
        try {
          // Use Firebase getBlob() to directly fetch the Blob from Storage

          const resizePath: any = getFilePathFromUrl(item.resize_url)
          const originzlPath: any = getFilePathFromUrl(item.url)

          // const resizeRef = ref(storage, resizePath); // Reference for resized image/video
          // const originalRef = ref(storage, originzlPath); // Reference for original image/video

          // console.log("resizeRef", resizeRef);
          // console.log("originalRef", originalRef);

          const resizeBlob = await fetchBlob(item.resize_url);
          const originalBlob = await fetchBlob(item.url);

          // Set appropriate file type and extension based on isVideo flag
          const fileType = item.isVideo ? "video/mp4" : "image/jpeg";
          const fileExtension = item.isVideo ? "mp4" : "jpg";

          // Convert blobs to files with appropriate names and types
          const resizeFile = new File([resizeBlob], `resize_file.${fileExtension}`, {
            type: fileType,
          });
          const originalFile = new File([originalBlob], `original_file.${fileExtension}`, {
            type: fileType,
          });

          // Upload both files and get new URLs
          const newResizeUrl = await handleUploadNewFile(resizeFile); // Replace with your upload function
          const newOriginalUrl = await handleUploadNewFile(originalFile); // Replace with your upload function

          return {
            ...item,
            resize_url: newResizeUrl,
            url: newOriginalUrl,
          };
        } catch (error) {
          console.error("Error processing media item:", error);
          // Return original URLs if processing fails
          return item;
        }
      })
    );

    return newMediaArray;
  } catch (error) {
    console.error("Error in generateNewMedia:", error);
    return [];
  }
};

export const handleUploadNewFile = async (file: File) => {
  const storage = getStorage();
  // Generate timestamp and create new filename
  // const timestamp = Date.now();
  const fileExtension = file.name.split(".").pop();
  const newFileName = `image_${uuidv4()}.${fileExtension}`;
  const storageRef = ref(storage, `uploads/finding/${newFileName}`);

  try {
    const snapshot = await uploadBytes(storageRef, file);
    const downloadURL = await getDownloadURL(snapshot.ref);

    return downloadURL;
  } catch (error) {
    throw error;
  }
};

const fetchBlob = async (url: string): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    xhr.responseType = 'blob';

    xhr.onload = () => {
      if (xhr.status === 200) {
        resolve(xhr.response); // Resolving the blob on successful response
      } else {
        reject(new Error('Failed to fetch blob')); // Reject if the status is not OK
      }
    };

    xhr.onerror = () => {
      reject(new Error('Network error'));
    };

    xhr.open('GET', url);
    xhr.send();
  });
};
