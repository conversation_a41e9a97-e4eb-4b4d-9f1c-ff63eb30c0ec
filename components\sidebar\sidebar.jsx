"use client";
import React, { useState, useEffect, useRef } from "react";
import Link from "next/link";
import Image from "next/image";
import { Open_Sans } from "next/font/google";
import { useRouter, usePathname } from "next/navigation";
import { logout, signOutUser } from "@/src/services/auth.api";
import { useTranslation } from "react-i18next";
import Logout from "@/components/logoutModal/logoutModal";
import { useLoaderContext } from "@/context/LoaderContext";
import { Button, message, Tooltip } from "antd";
import { LeftOutlined, RightOutlined } from "@ant-design/icons";
import { useSidebarContext } from "@/context/sidebarContext";

const OpenSans = Open_Sans({ weight: ["300", "400", "500"], subsets: ["latin"] });

const Sidebar = () => {
  const [selectedItem, setSelectedItem] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const router = useRouter();
  const { t } = useTranslation();
  const { loader, setLoader } = useLoaderContext();
  const { isCollapsed, setIsCollapsed, setIsJoyrideDisplayed } = useSidebarContext();

  const path = usePathname();
  const firstRender = useRef(false)

  useEffect(() => {
      if (!firstRender.current) {
        firstRender.current = true;
        const isDisplayed = localStorage.getItem("isJoyrideDisplayed");
        if (isDisplayed && isDisplayed === "false") {
          setIsJoyrideDisplayed(false);
          localStorage.setItem("isJoyrideDisplayed", "true");
        } else if (isDisplayed && isDisplayed === "true") {
          setIsJoyrideDisplayed(true);
        } else {
          localStorage.setItem("isJoyrideDisplayed", "true");
          setIsJoyrideDisplayed(false);
        }
      }
    }, []);

  useEffect(() => {
    if (
      path !== "/newInspection/newFindings" &&
      path !== "/newInspection/editFinding"
    ) {
      sessionStorage.clear();
      localStorage.removeItem("findingId");
      localStorage.removeItem("isFindingUpdate");
      localStorage.removeItem("isMeasureUpdate");
      localStorage.removeItem("isAddingMeasure");
      localStorage.removeItem("addNew");
      localStorage.removeItem("parent_finding_id");
      localStorage.removeItem("updateFindingObj");
      localStorage.removeItem("decompositionItemMedia");
      localStorage.removeItem("group");
      localStorage.removeItem("parentId");
      localStorage.removeItem("FindingTypeId");
    }
    if (path !== "/newInspection") {
      localStorage.removeItem("reportTypeId");
    }
    const page = path.split("/")[1];

    if (page && page !== "" && page !== "users") {
      setSelectedItem(page);
    }

    const collapsed = localStorage.getItem("collapsed");
    if (collapsed)
      collapsed === "true" ? setIsCollapsed(true) : setIsCollapsed(false);
  }, []);

  const handleButtonClick = (button) => {
    setSelectedItem(button);
  };

  const handleLogout = async (e) => {
    e.preventDefault();
    try {
      setLoader(true);
      const res = await signOutUser(router);
      if (res) {
        setLoader(false);
        setIsModalOpen(false);
      } else {
        setLoader(false);
        setIsModalOpen(false);
        message.error(t("Something went wrong, try again later!"));
      }
    } catch (error) {
      setLoader(false);
      if (error instanceof Error) {
      } else {
        message.error(t("An unknown error occurred."));
      }
    }
  };

  const handleLogoutModal = async () => {
    setIsModalOpen(true);
  };

  if (!isCollapsed) {
    return (
      <div className="w-[16%] h-screen bg-[#2F80ED0D]">
        <div className="h-[60px] flex items-center justify-center pr-4 border-b relative">
          <Link href={"/home"}>
            <Image
              width={94.14}
              height={32}
              className="w-[94.14px] h-auto"
              src="/images/sideBar/sidebarLogo.svg"
              alt="logo"
            />
          </Link>
          <Tooltip title={t("Close")} placement="right">
            <Button
              type="primary"
              className="p-0 w-[25px] h-[25px] absolute -right-3 z-10"
              onClick={() => {
                localStorage.setItem("collapsed", "true");
                setIsCollapsed(true);
              }}
            >
              <LeftOutlined />
            </Button>
          </Tooltip>
        </div>

        <div
          className={`h-[calc(100%-60px)] px-4 py-8 flex flex-col justify-between ${OpenSans.className}`}
        >
          <div className={`w-full`}>
            <div
              className={`w-full p-3 pl-4  transition-all rounded-lg flex gap-2 mb-2 cursor-pointer ${
                selectedItem === "home" ||
                selectedItem === "project" ||
                selectedItem === "newInspection"
                  ? "bg-[#2F80ED]"
                  : "hover:bg-[#2f81ed11]"
              }`}
              onClick={() => {
                handleButtonClick("home");
                router.push("/home");
              }}
            >
              <Image
                width={20}
                height={20}
                className="w-[20px] h-[20px]"
                src={
                  selectedItem === "home" ||
                  selectedItem === "project" ||
                  selectedItem === "newInspection"
                    ? "/images/home.png"
                    : "/images/sideBar/homeG.svg"
                }
                alt="home"
              />
              <p
                className={`${
                  selectedItem === "home" ||
                  selectedItem === "project" ||
                  selectedItem === "newInspection"
                    ? "text-white"
                    : "text-[#626d7d]"
                } text-[14px] font-[500]`}
              >
                {t("Home")}
              </p>
            </div>
            {/* <div
              className={`w-full p-3 pl-4  transition-all rounded-lg flex gap-2 mb-2 cursor-pointer ${
                selectedItem === "project" || selectedItem === "newInspection"
                  ? "bg-[#2F80ED]"
                  : "hover:bg-[#2f81ed11]"
              }`}
              onClick={() => {
                handleButtonClick("project");
                router.push("/project/newProject");
              }}
            >
              <Image
                width={20}
                height={20}
                className="w-[20px] h-[20px]"
                src={
                  selectedItem === "project" || selectedItem === "newInspection"
                    ? "/images/sideBar/projectWhite.png"
                    : "/images/sideBar/projectGray.png"
                }
                // src={
                //   selectedItem === "project" ||
                //   selectedItem === "newInspection" ||
                //   selectedItem === "inspections"
                //     ? "/images/sideBar/DocumentW.svg"
                //     : "/images/Document.png"
                // }
                alt="home"
              />
              <p
                className={`${
                  selectedItem === "project" || selectedItem === "newInspection"
                    ? "text-white"
                    : "text-[#626d7d]"
                } text-[14px] font-[500]`}
              >
                {t("Project")}
              </p>
            </div> */}
            <div
              className={`w-full p-3 pl-4 transition-all rounded-lg flex gap-2 mb-2 cursor-pointer ${
                selectedItem === "inspections"
                  ? "bg-[#2F80ED]"
                  : "hover:bg-[#2f81ed11]"
              }`}
              onClick={() => {
                handleButtonClick("inspections");
                router.push("/inspections");
              }}
            >
              <Image
                width={20}
                height={20}
                className="w-[20px] h-[20px]"
                src={
                  selectedItem === "inspections"
                    ? "/images/sideBar/DocumentW.svg"
                    : "/images/Document.png"
                }
                alt="doc"
              />
              <p
                className={`${
                  selectedItem === "inspections"
                    ? "text-white"
                    : "text-[#626d7d]"
                } text-[14px] font-[500] w-[150px]`}
              >
                {t("Inspections")}
              </p>
            </div>
            <div
              className={`w-full p-3 pl-4 transition-all rounded-lg flex gap-2 mb-2 cursor-pointer ${
                selectedItem === "profile"
                  ? "bg-[#2F80ED]"
                  : "hover:bg-[#2f81ed11]"
              }`}
              onClick={() => {
                handleButtonClick("profile");
                router.push("/profile");
              }}
            >
              <Image
                width={20}
                height={20}
                className="w-[20px] h-[20px]"
                src={
                  selectedItem === "profile"
                    ? "/images/sideBar/ProfileW.svg"
                    : "/images/Profile.png"
                }
                alt="profile"
              />
              <p
                className={`${
                  selectedItem === "profile" ? "text-white" : "text-[#626d7d]"
                } text-[14px] font-[500]`}
              >
                {t("Profile")}
              </p>
            </div>
          </div>
          <div className="w-full">
            <Button
              onClick={handleLogoutModal}
              type="primary"
              className={`${OpenSans.className} custom-button w-full rounded-xl text-[15px] border-[#B4B4B4] border-opacity-30 bg-[#2F80ED] text-white h-[43px]`}
            >
              <Image
                width={20}
                height={20}
                className="w-[20px] h-[20px]"
                src={"/images/logout.svg"}
                alt="logout"
              />
              <p className={`text-white text-[14px] font-[600]`}>
                {t("Logout")}
              </p>
            </Button>
          </div>
        </div>

        <Logout
          handleLogout={handleLogout}
          isModalOpen={isModalOpen}
          setIsModalOpen={setIsModalOpen}
        />
      </div>
    );
  } else {
    return (
      <div className="w-[60px] h-screen bg-[#2F80ED0D]">
        <div className="w-full h-[60px] flex items-center justify-center border-b relative">
          <Tooltip title={t("Open")} placement="right">
            <Button
              type="primary"
              className="p-0 w-[25px] h-[25px]"
              onClick={() => {
                localStorage.setItem("collapsed", "false");
                setIsCollapsed(false);
              }}
            >
              <RightOutlined />
            </Button>
          </Tooltip>
        </div>

        <div
          className={`h-[calc(100%-60px)] py-8 flex flex-col justify-between ${OpenSans.className}`}
        >
          <div className={`w-full flex flex-col items-center`}>
            <div
              className={`w-[43px] h-[43px] rounded-xl transition-all flex gap-2 justify-center items-center mb-2 cursor-pointer ${
                selectedItem === "home" ||
                selectedItem === "project" ||
                selectedItem === "newInspection"
                  ? "bg-[#2F80ED]"
                  : "hover:bg-[#2f81ed11]"
              }`}
              onClick={() => {
                handleButtonClick("home");
                router.push("/home");
              }}
            >
              <Tooltip title={t("Home")} placement="right">
                <div className="w-full h-full flex justify-center items-center">
                  <Image
                    width={20}
                    height={20}
                    className="w-[20px] h-[20px]"
                    src={
                      selectedItem === "home" ||
                      selectedItem === "project" ||
                      selectedItem === "newInspection"
                        ? "/images/home.png"
                        : "/images/sideBar/homeG.svg"
                    }
                    alt="home"
                  />
                </div>
              </Tooltip>
            </div>

            <div
              className={`w-[43px] h-[43px] rounded-xl transition-all flex gap-2 justify-center items-center mb-2 cursor-pointer ${
                selectedItem === "inspections"
                  ? "bg-[#2F80ED]"
                  : "hover:bg-[#2f81ed11]"
              }`}
              onClick={() => {
                handleButtonClick("inspections");
                router.push("/inspections");
              }}
            >
              <Tooltip title={t("Inspections")} placement="right">
                <div className="w-full h-full flex justify-center items-center">
                  <Image
                    width={20}
                    height={20}
                    className="w-[20px] h-[20px]"
                    src={
                      selectedItem === "inspections"
                        ? "/images/sideBar/DocumentW.svg"
                        : "/images/Document.png"
                    }
                    alt="doc"
                  />
                </div>
              </Tooltip>
            </div>

            <div
              className={`w-[43px] h-[43px] rounded-xl transition-all flex gap-2 justify-center items-center mb-2 cursor-pointer ${
                selectedItem === "profile"
                  ? "bg-[#2F80ED]"
                  : "hover:bg-[#2f81ed11]"
              }`}
              onClick={() => {
                handleButtonClick("profile");
                router.push("/profile");
              }}
            >
              <Tooltip title={t("Profile")} placement="right">
                <div className="w-full h-full flex justify-center items-center">
                  <Image
                    width={20}
                    height={20}
                    className="w-[20px] h-[20px]"
                    src={
                      selectedItem === "profile"
                        ? "/images/sideBar/ProfileW.svg"
                        : "/images/Profile.png"
                    }
                    alt="profile"
                  />
                </div>
              </Tooltip>
            </div>
          </div>
          <div className="w-full flex justify-center">
            <Button
              onClick={handleLogoutModal}
              type="primary"
              className={`${OpenSans.className} custom-button p-0 rounded-xl text-[15px] border-[#B4B4B4] border-opacity-30 bg-[#2F80ED] text-white w-[43px] h-[43px]`}
            >
              <Tooltip title={t("Logout")} placement="right">
                <div className="w-full h-full flex justify-center items-center">
                  <Image
                    width={20}
                    height={20}
                    className="w-[20px] h-[20px]"
                    src={"/images/logout.svg"}
                    alt="logout"
                  />
                </div>
              </Tooltip>
            </Button>
          </div>
        </div>

        <Logout
          handleLogout={handleLogout}
          isModalOpen={isModalOpen}
          setIsModalOpen={setIsModalOpen}
        />
      </div>
    );
  }
};

export default Sidebar;
