import React, { useRef, useState, useEffect } from "react";
import {
  GoogleM<PERSON>,
  <PERSON><PERSON>,
  InfoWindow,
  useLoadScript,
} from "@react-google-maps/api";
import Image from "next/image";

const mapContainerStyle = {
  width: "100%",
  height: "100%",
  borderRadius: "12px",
};

const defaultCenter = {
  lat: 52.3676, // Amsterdam, Netherlands  
  lng: 4.9041,  // Amsterdam, Netherlands
};

const greenMarker = "/images/home/<USER>";
const blueMarker = "/images/home/<USER>";

interface Location {
  lat: number;
  lon: number;
}

interface Inspection {
  id: string;
  location: Location;
  isCompleted: boolean;
  name: string;
  reportType: string;
  image: string;
  finding_order: any;
}

interface InspectionMapProps {
  inspections: Inspection[];
  googleMapsApiKey: any;
  handleInspectionClick: (id: string, name: string, orderArray: any) => void;
}

const blueDot = "/images/home/<USER>";

const InspectionMap = ({
  inspections,
  googleMapsApiKey,
  handleInspectionClick,
}: InspectionMapProps) => {
  const { isLoaded, loadError } = useLoadScript({
    googleMapsApiKey,
  });

  const mapRef = useRef<google.maps.Map | null>(null);
  const [selectedInspection, setSelectedInspection] = useState<Inspection | null>(null);
  const [currentLocation, setCurrentLocation] = useState<{ lat: number; lng: number } | null>(null);

  useEffect(() => {
      // Fetch the user's current location
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const userLocation = {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          };
          setCurrentLocation(userLocation);
        },
        (error) => {
          const fallbackLocation = { lat: 52.3676, lng: 4.9041 }; // Fallback: Amsterdam, Netherlands
          setCurrentLocation(fallbackLocation);
        }
      );
  }, [location]);

  // Function to adjust map bounds
  const fitMapBounds = () => {
    console.log('fitMapBounds called, inspections length:', inspections?.length);
    console.log('Sample inspection structure:', inspections?.[0]);
    console.log('All inspection keys:', Object.keys(inspections?.[0] || {}));
    
    if (mapRef.current && inspections && inspections.length > 0) {
      const validInspections = inspections.filter(inspection => inspection.location);
      console.log('Valid inspections with location:', validInspections.length);
      
      if (validInspections.length > 0) {
        const bounds = new google.maps.LatLngBounds();
        validInspections.forEach((inspection) => {
          bounds.extend(new google.maps.LatLng(inspection.location.lat, inspection.location.lon));
        });
        console.log('Fitting bounds to inspections');
        mapRef.current.fitBounds(bounds);
      } else {
        console.log('No valid inspections, staying with default center');
      }
    } else {
      console.log('No inspections or no mapRef, using default center');
    }
  };

  // Call fitMapBounds when the inspections array changes
  useEffect(() => {
    if (isLoaded) fitMapBounds();
  }, [isLoaded, inspections]);

  // Move mapOptions inside the component
  const mapOptions = {
    disableDefaultUI: true,
    zoomControl: true,
    mapTypeControl: true,
    mapTypeControlOptions: isLoaded ? {
      style: google.maps.MapTypeControlStyle.HORIZONTAL_BAR,
      position: google.maps.ControlPosition.TOP_RIGHT,
      mapTypeIds: ["roadmap", "satellite"],
    } : {},
  };

  if (loadError) return <div>Error loading map</div>;
  if (!isLoaded) return <div>Loading Map...</div>;

  return (
    <GoogleMap
      mapContainerStyle={mapContainerStyle}
      center={defaultCenter}
      zoom={7}
      options={mapOptions}
      onLoad={(map) => {
        console.log('Map loaded with default center:', defaultCenter);
        mapRef.current = map;
        fitMapBounds(); // Fit bounds once map is loaded
      }}
    >


      {/* Blue dot for current location */}
      {currentLocation && (
        <Marker
          position={currentLocation}
          icon={{
            url: blueDot, // Blue dot icon
          }}
        />
      )}

      {inspections.map((inspection) =>
        inspection.location ? (
          <Marker
            key={inspection.id}
            position={{
              lat: inspection.location.lat,
              lng: inspection.location.lon,
            }}
            icon={{
              url: inspection.isCompleted ? greenMarker : blueMarker,
            }}
            onClick={() => setSelectedInspection(inspection)}
          />
        ) : null
      )}

      {selectedInspection && (
        <InfoWindow
          position={{
            lat: selectedInspection.location.lat,
            lng: selectedInspection.location.lon,
          }}
          onCloseClick={() => setSelectedInspection(null)}
        >
          <div
            onClick={() => handleInspectionClick(selectedInspection.id, selectedInspection.name, selectedInspection?.finding_order)}
            style={{ cursor: "pointer" }}
          >
            <img
              src={selectedInspection.image}
              alt={selectedInspection.name}
              width={200}
              height={100}
              className="w-[200px] h-[100px] object-contain bg-zinc-200 rounded-md"
            />
            <h1 className="text-[16px] w-[200px] leading-[20.8px] text-center break-words font-[400] my-2">
              {selectedInspection.name}
            </h1>
          </div>
        </InfoWindow>
      )}
    </GoogleMap>
  );
};

export default InspectionMap;
