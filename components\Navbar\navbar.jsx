import React, { useEffect, useRef, useState } from "react";
import { Input, Avatar, Tooltip } from "antd";
import Image from "next/image";
import { Open_Sans } from "next/font/google";
import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";
import { message } from "antd";
import { auth } from "@/firebase.config";
import { waitForAuthState } from "@/src/libs/helpers";

const OpenSans = Open_Sans({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

const Navbar = ({ search, searchInspection, setSearch }) => {
  const router = useRouter();
  const { t } = useTranslation();
  const [name, setName] = useState("");

  const effectRan = useRef(false);

  useEffect(() => {
    if (effectRan.current === false) {
      
      checkAuth()      
      
      effectRan.current = true;
    }
  }, []);

  const checkAuth = async () => {
    await waitForAuthState()

    if (auth.currentUser) {
      // startTokenRefresh();
      // message.success("Available")
    } else {
      message.error(t("Session expired."));
      localStorage.clear()
      sessionStorage.clear()
      router.push("/");
    }
  }

  useEffect(() => {
    const userName = localStorage.getItem("ScalarUserName");
    if (userName && userName !== "null") {
      setName(userName);
    } else {
      const email = localStorage.getItem("ScalarUserEmail");
      setName(email);
    }
  }, []);

  return (
    <div className="w-full h-[60px] border-b text-black">
      <div className="w-full h-full flex justify-between">
        <div className={`w-[45%] h-full p-3 px-10 ${OpenSans.className}`}>
          {search && (
            <Input
              className="px-3 h-[35px] bg-[#2F80ED0D] border-none"
              placeholder={t("Search")}
              value={searchInspection}
              suffix={
                <Image
                  width={12}
                  height={12}
                  className={`${
                    searchInspection !== ""
                      ? "w-[30px] relative left-2 cursor-pointer"
                      : "w-[12px]"
                  } ${searchInspection !== "" ? "h-[30px]" : "h-[12px]"}`}
                  src={
                    searchInspection.length !== 0
                      ? "/images/cancel.svg"
                      : "/images/Icon.png"
                  }
                  alt="icon"
                  onClick={
                    searchInspection !== "" ? () => setSearch("") : () => {}
                  }
                />
              }
              onChange={(e) => setSearch(e.target.value)}
            />
          )}
        </div>
        <div className="w-[10%] h-full flex items-center justify-center profile">
          <Tooltip placement="left" title={t("Profile")}>
            <Avatar
              style={{
                backgroundColor: "#2F80ED",
                verticalAlign: "middle",
              }}
              size="large"
              className={`text-[20px] leading-[20px] font-[700] cursor-pointer ${OpenSans.className}`}
              onClick={() => router.push("/profile")}
            >
              {name !== "null" ? name?.slice(0, 1).toUpperCase() : "!"}
            </Avatar>
          </Tooltip>
        </div>
      </div>
    </div>
  );
};

export default Navbar;
