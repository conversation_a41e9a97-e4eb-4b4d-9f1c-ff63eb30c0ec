import React, { useState } from "react";
import { Modal, Checkbox, Button } from "antd";
import { Open_Sans } from "next/font/google";
import Link from "next/link";

const OpenSans = Open_Sans({
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
});

const PrivacyModal = ({
  isPrivacyModalVisible,
  handlePrivacyOk,
  handlePrivacyCancel,
  t,
}: any) => {
  const [isChecked, setIsChecked] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleOk = async () => {
    setIsLoading(true);
    await handlePrivacyOk();
    setIsLoading(false);
  };

  return (
    <Modal
      title={
        <div className={`text-center text-[22px] ${OpenSans.className}`}>
          {t("Privacy Policy")}
        </div>
      }
      open={isPrivacyModalVisible}
      okButtonProps={{
        loading: isLoading,
      }}
      onCancel={handlePrivacyCancel}
      width={680}
      centered
      footer={null}
      closeIcon={null}
      className="custom-modal-sidebar"
      styles={{
        body: {
          maxHeight: "80vh",
          overflowY: "auto",
        },
      }}
      maskClosable={false}
    >
      <p className={`text-[14px] text-justify my-4 ${OpenSans.className}`}>
        Scalar B.V., located at Bisschopsweg 7, 3817BP Amersfoort, is
        responsible for the processing of personal data as reflected in this
        privacy statement.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        Your data is important. That&apos;s why we treat it that way. In this
        privacy statement, we tell you what data we use and how we use it. When
        processing personal data, Scalar adheres to the requirements of privacy
        legislation, including the General Data Protection Regulation (AVG). The
        privacy statement may change. The most up-to-date privacy statement can
        be found on our website (
        <Link
          href="https://www.scalar.agency/privacy-statement"
          target="_blank"
          className="text-blue-500"
        >
          https://www.scalar.agency/privacy-statement
        </Link>
        ). This privacy statement was last modified on 17-10-2024.
      </p>
      <p
        className={`text-[16px] font-[500] text-justify mb-2 ${OpenSans.className}`}
      >
        1. Contact details:
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        <Link
          href="https://scalar.agency"
          target="_blank"
          className="text-blue-500"
        >
          https://scalar.agency{" "}
        </Link>{" "}
        <br />
        Marijn van Hoek is the Data Protection Officer of Scalar B.V. He can be
        reached at{" "}
        <Link
          href="mailto:<EMAIL>"
          target="_blank"
          className="text-blue-500"
        >
          <EMAIL>
        </Link>
      </p>
      <p
        className={`text-[16px] font-[500] text-justify mb-2 ${OpenSans.className}`}
      >
        2. Personal data we process:
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        Scalar B.V. processes your personal data because your company uses our
        services and/or because you provide them to us yourself by any of the
        following actions:
      </p>
      <ul className="list-disc list-inside ml-4 mt-1">
        <li>You request content such as a white paper, video or checklist</li>
        <li>You register for one of our events or webinars</li>
        <li>
          You contact us (via a form, such as; the contact form, a demo request
          form or free trial request form)
        </li>
        <li>
          You have expressed by phone or verbally (at an event) that you would
          like to receive more information and/or make a follow-up appointment
          regarding our product or services
        </li>
        <li>You applied for a job with us</li>
        <li>
          We obtained your information through a public source, such as LinkedIn
        </li>
      </ul>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        You are not obliged to leave personal data, however, it is not possible
        to perform the above actions without sharing your data with us.
      </p>
      <p
        className={`text-[16px] font-[500] text-justify mb-2 ${OpenSans.className}`}
      >
        3. Below is an overview of the personal data we process:
      </p>
      <ul
        className={`list-disc list-inside ml-4 mt-1 text-[14px] text-justify mb-4 ${OpenSans.className}`}
      >
        <li>First and last name</li>
        <li>Phone number</li>
        <li>E-mail address</li>
        <li>Role</li>
        <li>
          Other personal data that you actively provide e.g. by becoming a
          customer, in correspondence, orally or by telephone
        </li>
        <li>
          Other personal data you actively provide by using our Scalar Inspect
          application, for example GPS location, photos and other information
          from the photo library on your (mobile) device.
        </li>
      </ul>
      <p
        className={`text-[16px] font-[500] text-justify mb-2 ${OpenSans.className}`}
      >
        4. For what purpose and on what basis we process personal data:
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        Scalar B.V. processes your personal data for the following purposes:
      </p>
      <ul className="list-disc list-inside ml-4 mt-1">
        <li>
          adding you to our mailing list to inform you via, among other things,
          our newsletter about relevant webinars and/or other (product)
          developments (basis: consent)
        </li>
        <li>
          Calling or emailing you when necessary to perform and improve our
          services (basis: consent)
        </li>
        <li>
          Inform you of changes to our services and products (basis: agreement)
        </li>
        <li>Allow you to create an account (basis: consent)</li>
        <li>
          Enable you to use the functionalities of the Scalar inspection app
          (basis: consent)
        </li>
        <li>
          Scalar B.V. analyzes your behavior on the website and in the Scalar
          Inspect mobile app in order to improve the website and app and tailor
          the offer of products and services to your preferences. (basis:
          consent)
        </li>
        <li>
          to be able to invite you and schedule follow-up actions to an
          application process (basis: consent)
        </li>
      </ul>
      <p
        className={`text-[16px] font-[500] text-justify mb-2 ${OpenSans.className}`}
      >
        5. How long we keep personal data:
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        Scalar B.V. does not retain your personal data longer than is strictly
        necessary to fulfill the purposes for which your data is collected. We
        use the following retention periods for the following (categories of)
        personal data:
      </p>
      <ul className="list-disc list-inside ml-4 mt-1">
        <li>
          Contacts who are in our database but have not had an active form of
          contact (such as a webinar or event registration, white paper
          download, free trial or demo request) with Scalar - 1 year
        </li>
        <li>
          Contacts who actively interacted with us (such as a webinar or event
          registration, white paper download, sales call, free trial or demo
          request) but did not become a customer - 2 years
        </li>
      </ul>
      <p
        className={`text-[16px] font-[500] text-justify mb-2 ${OpenSans.className}`}
      >
        6. Sharing personal data with third parties:
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        Scalar B.V. only provides third parties and only when necessary for the
        performance of our agreement with you or to comply with a legal
        obligation.
      </p>
      <p
        className={`text-[16px] font-[500] text-justify mb-2 ${OpenSans.className}`}
      >
        7. Cookies, or similar techniques, that we use:
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        Scalar B.V. uses functional, analytical and tracking cookies. A cookie
        is a small text file that is stored in the browser of your computer,
        tablet or smartphone the first time you visit this website. Scalar B.V.
        uses cookies with a purely technical functionality. These ensure that
        the website works properly and that, for example, your preferences are
        remembered. These cookies are also used to make the website work
        properly and to optimize it. In addition, we place cookies that track
        your surfing behavior so that we can offer tailored content and
        advertisements. During your first visit to our website we informed you
        about these cookies and asked your permission to place them. You can opt
        out of cookies by setting your internet browser to stop storing cookies.
        In addition, you can also delete any information previously stored via
        your browser settings.
      </p>
      <p
        className={`text-[16px] font-[500] text-justify mb-2 ${OpenSans.className}`}
      >
        8. No processing of data of persons under 16 years of age:
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        We do not intend to collect data from website visitors and app users
        under the age of 16. We encourage parents to be involved in their
        children&apos;s online activities in order to prevent Scalar from
        processing their personal data.
      </p>
      <p
        className={`text-[16px] font-[500] text-justify mb-2 ${OpenSans.className}`}
      >
        9. How we secure personal data:
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        Scalar B.V. takes the protection of your data seriously and takes
        appropriate measures to prevent abuse, loss, unauthorized access,
        unwanted disclosure and unauthorized modification. If you have the
        impression that your data is not properly secured or there are
        indications of abuse, please contact our customer service at
        <EMAIL>.
      </p>
      <p
        className={`text-[16px] font-[500] text-justify mb-2 ${OpenSans.className}`}
      >
        10. View, modify or delete data:
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        You have the right to view, correct or delete your personal data. You
        also have the right to withdraw your (possible) permission for data
        processing or to object to the processing of your personal data by
        Scalar B.V. and you have the right to data portability. This means that
        you can request us to send the personal data we have on you in a
        computer file to you or another organization named by you.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        You can send a request to inspect, correct, delete, transfer your
        personal data or request the revocation of your consent or objection to
        the processing of your personal data to: <EMAIL>. We will
        respond to your request as quickly as possible, but at the latest within
        four weeks.
      </p>
      <p className={`text-[14px] text-justify mb-16 ${OpenSans.className}`}>
        Scalar B.V. would also like to point out to you that you have the
        possibility to file a complaint with the national supervisory authority:
        the Authority for Personal Data. This can be done via the following
        link:
        <br />{" "}
        <Link
          href="https://autoriteitpersoonsgegevens.nl/nl/contact-met-de-autoriteit-persoonsgegevens/tip-ons"
          target="_blank"
          className="text-blue-500"
        >
          https://autoriteitpersoonsgegevens.nl/nl/contact-met-de-autoriteit-persoonsgegevens/tip-ons
        </Link>
      </p>
      <div className="w-full pt-6 px-6 flex justify-between items-center bg-white fixed-bottom-button">
        <Checkbox onChange={(e) => setIsChecked(e.target.checked)}>
          {t("Agree to the privacy policy")}
        </Checkbox>
        <Button
          type="primary"
          disabled={!isChecked}
          onClick={handleOk}
          className="custom-button-disable"
          loading={isLoading}
        >
          {t("Accept")}
        </Button>
      </div>
    </Modal>
  );
};

export default PrivacyModal;
