import React, { useEffect } from "react";
import { Divider, Radio, Skeleton, Table } from "antd";
import moment from "moment";
import { Timestamp } from "firebase/firestore";
import Image from "next/image";
import { useTranslation } from "react-i18next";

const InspectionsTable = ({
  searchedProjects,
  inspectionTypeList,
  inspections,
  setDeleteId,
  setIsModalOpen,
  handleProjectClick,
  selectedProjects,
  setSelectedProjects,
  loadedInspectionIds,
}: any) => {
  const { t } = useTranslation();

  const formatTimestamp = (timestamp: Timestamp) => {
    const milliseconds =
      timestamp.seconds * 1000 + timestamp.nanoseconds / 1000000;
    const parsedDate = moment(milliseconds);
    return parsedDate.format("D MMM YYYY");
  };
  const getInspectionTypeLabel = (record: any) => {
    return (
      inspectionTypeList
        ?.filter((item: any) => item.value === record.id)
        .map((item: any) => item.label)[0] || ""
    );
  };

  const calculateProgress = (projectId: string, inspectionsList: any) => {
    const completed = inspections.filter((item: any) => {
      if (item.project.id === projectId) {
        return inspectionsList.some(
          (element: any) => element.id === item.id && item.isCompleted
        );
      }
      return false;
    });

    return completed.length;
  };

  const columns: any = [
    {
      title: <>{t("Project Name")}</>,
      dataIndex: "name",
      render: (text: string) => text,
      onCell: (record: any) => {
        return {};
      },
      sorter: (a: any, b: any) => a.name.localeCompare(b.name),
      sortDirections: ["ascend", "descend"],
    },
    {
      title: <>{t("Inspection Type")}</>,
      dataIndex: "inspection_type",
      render: (record: any) => <p>{getInspectionTypeLabel(record)}</p>,
      sorter: (a: any, b: any) => a.id.localeCompare(b.id),
      sortDirections: ["ascend", "descend"],
    },
    {
      title: <>{t("Progress")}</>,
      render: (record: any) => {
        // Check if this specific project's inspections are loaded
        const allInspectionsLoaded = record?.inspections?.every((insp: any) =>
          loadedInspectionIds.has(insp.id)
        );

        // Show skeleton only for projects whose inspections aren't loaded
        if (!allInspectionsLoaded && record?.inspections?.length > 0) {
          return (
            <div className="flex items-center">
              {/* <div className="w-12 h-5 bg-gray-200 shimmer rounded"></div> */}
              <Skeleton.Button active size={"small"} className="!w-12" />
            </div>
          );
        }

        return (
          <p>
            {calculateProgress(record.id, record?.inspections)}/
            {record?.inspections ? record?.inspections?.length : 0}
          </p>
        );
      },
    },
    {
      title: <>{t("Date")}</>,
      render: (record: any) => <p>{formatTimestamp(record.created_at)}</p>,
      sorter: (a: any, b: any) => {
        const aMilliseconds =
          a.created_at.seconds * 1000 + a.created_at.nanoseconds / 1000000;
        const bMilliseconds =
          b.created_at.seconds * 1000 + b.created_at.nanoseconds / 1000000;
        return aMilliseconds - bMilliseconds; // Sort by date (numerical comparison)
      },
      sortDirections: ["ascend", "descend"],
    },
    // {
    //   title: <>{t("Action")}</>,
    //   width: 150,
    //   fixed: "right",
    //   render: (_: any, record: any) => (
    //     <button
    //       className="flex gap-2 text-red-500"
    //       onClick={(e) => {
    //         e.stopPropagation();
    //         setDeleteId(record.id);
    //         setIsModalOpen(true);
    //       }}
    //       aria-label="delete"
    //     >
    //       <Image
    //         src="/images/home/<USER>"
    //         alt="logo"
    //         width={24}
    //         height={24}
    //         className="w-[24px] h-[24px]"
    //       />{" "}
    //       {t("Delete")}
    //     </button>
    //   ),
    //   // Apply the background color conditionally to the entire cell
    //   onCell: (record: any) => {
    //     return {
    //       style: {
    //         backgroundColor:
    //           record.completion && !selectedProjects.includes(record.id)
    //             ? "#e5e5e5"
    //             : "",
    //       },
    //     };
    //   },
    // },
  ];

  const rowSelection: any["rowSelection"] = {
    onChange: (selectedRowKeys: React.Key[], selectedRows: any) => {
      setSelectedProjects(selectedRowKeys);
    },
    getCheckboxProps: (record: any) => ({
      disabled: record.name === "Disabled User", // Column configuration not to be checked
      name: record.name,
    }),
  };

  return (
    <Table
      rowKey="id"
      rowSelection={{ type: "checkbox", ...rowSelection }}
      columns={columns}
      dataSource={searchedProjects}
      pagination={false}
      className="mb-10"
      rowClassName={(record: any) =>
        record.completion ? "bg-[#e5e5e5] no-hover" : ""
      }
      onRow={(record) => ({
        onClick: () => {
          handleProjectClick(record.id, record.name, record.inspection_type.id);
        },
        className: "cursor-pointer",
      })}
      sticky
    />
  );
};

export default InspectionsTable;
