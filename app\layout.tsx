import type { Metada<PERSON> } from "next";
import { Open_Sans } from "next/font/google";
import "./globals.css";
import { LoaderContextProvider } from "@/context/LoaderContext";
import { FindingContextProvider } from "@/context/findingContext";
import { SidebarContextProvider } from "@/context/sidebarContext";
import { LanguageContextProvider } from "@/context/LanguageContext";
import ClientLayout from '@/components/clientLayout/clientLayout'
// import '@/src/i18n';

const openSans = Open_Sans({ weight: ["300", "400", "500", "700"], subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Scalar Inspect",
  description: "Scalar Inspection Web",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <LoaderContextProvider>
        <FindingContextProvider>
          <SidebarContextProvider>
            <LanguageContextProvider>
              <body className={openSans.className}>
                <ClientLayout>
                  {children}
                </ClientLayout>
              </body>
            </LanguageContextProvider>
          </SidebarContextProvider>
        </FindingContextProvider>
      </LoaderContextProvider>
    </html>
  );
}
