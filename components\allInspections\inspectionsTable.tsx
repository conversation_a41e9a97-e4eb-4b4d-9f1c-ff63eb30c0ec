import React, { useEffect, useState } from "react";
import { Divider, Radio, Table } from "antd";
import moment from "moment";
import { Timestamp } from "firebase/firestore";
import Image from "next/image";
import { useTranslation } from "react-i18next";

const InspectionsTable = ({
  Inspections,
  inspectionTypeList,
  projectNames,
  setDeleteId,
  setProjectId,
  setIsModalOpen,
  handleInspectionClick,
  selectedInspections,
  setSelectedInspections,
  selectedInspectionObj,
  setSelectedInspectionObj,
}: any) => {

  const { t } = useTranslation();

  const formatTimestamp = (timestamp: Timestamp) => {
    const milliseconds =
      timestamp.seconds * 1000 + timestamp.nanoseconds / 1000000;
    const parsedDate = moment(milliseconds);
    return parsedDate.format("D MMM YYYY");
  };

  const getInspectionTypeLabel = (record: any) => {
    return (
      inspectionTypeList
        ?.filter((item: any) => item.value === record.id)
        .map((item: any) => item.label)[0] || "-"
    );
  };

  const getProjectName = (projectId: any) => {
    return projectNames[projectId] || "-"
  }

  const columns: any = [
    {
      title: <>{t("Inspection Name")}</>,
      dataIndex: "name",
      render: (name: any) => <p>{name}</p>,
      onCell: (record: any) => {
        if (record.name === "Demo inspectie") {
          return { className: "joyride-demo-inspection-cell" };
        }
        return {};
      },
      sorter: (a: any, b: any) => a.name.localeCompare(b.name), // Use localeCompare for string sorting
      sortDirections: ["ascend", "descend"],
    },
    {
      title: <>{t("Inspection Type")}</>,
      dataIndex: "reporttype",
      render: (record: any) => <p>{getInspectionTypeLabel(record)}</p>,
      sorter: (a: any, b: any) =>
        (a.id).localeCompare(b.id),
      sortDirections: ["ascend", "descend"],
    },
    {
      title: <>{t("Project")}</>,
      dataIndex: "project",
      render: (record: any) => <p>{getProjectName(record.id)}</p>,
      sorter: (a: any, b: any) =>
        getProjectName(a.project.id).localeCompare(getProjectName(b.project.id)),
      sortDirections: ["ascend", "descend"],
    },
    {
      title: <>{t("Date")}</>,
      render: (record: any) => <p>{formatTimestamp(record.created_at)}</p>,
      sorter: (a: any, b: any) => {
        const aMilliseconds =
          a.created_at.seconds * 1000 + a.created_at.nanoseconds / 1000000;
        const bMilliseconds =
          b.created_at.seconds * 1000 + b.created_at.nanoseconds / 1000000;
        return aMilliseconds - bMilliseconds; // Sort by date (numerical comparison)
      },
      sortDirections: ["ascend", "descend"],
    },
  ];

  const rowSelection: any["rowSelection"] = {
    onChange: (selectedRowKeys: React.Key[], selectedRows: any) => {
      setSelectedInspections(selectedRowKeys);
      setSelectedInspectionObj(selectedRows)
    },
    getCheckboxProps: (record: any) => ({
      disabled: record.name === "Disabled User", // Column configuration not to be checked
      name: record.name,
    }),
  };

  return (
    <Table
      rowKey="id"
      rowSelection={{ type: "checkbox", ...rowSelection }}
      columns={columns}
      dataSource={Inspections}
      pagination={false}
      className="mb-10"
      rowClassName={(record: any) =>
        record.isCompleted ? "bg-[#e5e5e5] no-hover" : ""
      }
      onRow={(record) => ({
        onClick: () => handleInspectionClick(record),
        className: "cursor-pointer",
      })}
      sticky
    />
  );
};

export default InspectionsTable;
