"use client";
import React, { useEffect, useState, useRef, useCallback } from "react";
import Navbar from "@/components/Navbar/navbar";
import Sidebar from "@/components/sidebar/sidebar";
import { Open_Sans } from "next/font/google";
import Image from "next/image";
import { Button, Input, Modal, Spin } from "antd";
import { useRouter } from "next/navigation";
import { useLoaderContext } from "@/context/LoaderContext";
import Loader from "@/components/loader/loader";
import { message } from "antd";
import {
  getProjects,
  deleteProject,
  fetchUser,
} from "@/src/services/project.api";
import { fetchInspectionsByIds } from "@/src/services/home.api";
import { inspectionTypeList } from "@/src/libs/constants";
import DeleteModal from "./deleteModal";
import { useTranslation } from "react-i18next";
import { handleChangeLanguage } from "@/context/LanguageContext";
import TNCModal from "./tncModal";
import PrivacyModal from "./privacyModal";
import ProjectTable from "./projectTable";
import {
  getUserReportTypes,
  updateUserTermsFlag,
} from "@/src/services/auth.api";
import ExportModal from "./exportModal";
import ActionButtons from "./actionButtons";
import { useSidebarContext } from "@/context/sidebarContext";
import InfiniteScroll from "react-infinite-scroll-component";
import { LoadingOutlined } from "@ant-design/icons";
import debounce from "debounce";

const OpenSans = Open_Sans({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

const Home = () => {
  const [projects, setProjects] = useState<any>([]);
  const [searchedProjects, setSearchedProjects] = useState<any>([]);
  const [isTermsModalVisible, setIsTermsModalVisible] = useState(false);
  const [isPrivacyModalVisible, setIsPrivacyModalVisible] = useState(false);
  const [search, setSearch] = useState<any>("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [deleteId, setDeleteId] = useState("");
  const [refresh, setRefresh] = useState(false);
  const [inspectionTypeList, setInspectionTypeList] = useState<any>(null);
  const [inspections, setInspections] = useState<any>([]);
  const [selectedProjects, setSelectedProjects] = useState<any>([]);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [isActionMenuVisible, setIsActionMenuVisible] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  const { isCollapsed } = useSidebarContext();

  const [hasMany, setHasMany] = useState(false);
  const [lastDoc, setLastDoc] = useState<string | null>(null);
  const [searchLoader, setSearchLoader] = useState(false);

  const router = useRouter();
  const { t } = useTranslation();

  const { loader, setLoader } = useLoaderContext();

  // Instead of a single loading state, track loaded inspection IDs
  const [loadedInspectionIds, setLoadedInspectionIds] = useState<Set<string>>(
    new Set()
  );

  const fetchProjects = async (userId: any) => {
    try {
      const params = {
        search: search.toLowerCase(),
        userId,
        isForFirst: true,
        lastDocument: lastDoc,
        limitValue: 20,
      };

      // Fetch and update projects immediately
      const projectsArray: any = await getProjects(params);
      if (projectsArray) {
        // Set projects immediately to display in table - do this first
        setProjects(projectsArray.list);
        setSearchedProjects(projectsArray.list);
        setHasMany(projectsArray.hasMore);
        setLastDoc(projectsArray.lastDocument);
        setLoader(false);

        // Then load inspection types
        const reportTypesString: any = localStorage.getItem("inspectionTypes");
        const reportTypes = JSON.parse(reportTypesString);
        setInspectionTypeList(reportTypes);

        // Fetch inspections in background after UI is updated
        fetchInspectionsForProjects(projectsArray.list);
      }
    } catch (error) {
      message.error(t("Something went wrong!"));
    }
  };

  const fetchInspectionsForProjects = async (projectsList: any[]) => {
    const inspectionIds: string[] = [];

    // Collect all inspection IDs that need to be fetched
    projectsList.forEach((project: any) => {
      if (project.inspections && project.inspections.length > 0) {
        const ids = project.inspections.map((insp: any) => insp?.id);
        inspectionIds.push(...ids);
      }
    });

    const uniqueIds = Array.from(new Set(inspectionIds));

    // Skip if no inspections to fetch
    if (uniqueIds.length === 0) return;

    // Use setTimeout to defer the inspection fetching to the next event loop cycle
    // This ensures the UI can update and respond to scroll events
    setTimeout(() => {
      // Process in smaller batches to avoid overwhelming the system
      const fetchBatches = async () => {
        try {
          const batchSize = 300;
          for (let i = 0; i < uniqueIds.length; i += batchSize) {
            const batchIds = uniqueIds.slice(i, i + batchSize);
            const res: any = await fetchInspectionsByIds(batchIds);

            if (res) {
              // Update inspections data
              setInspections((prevInspections: any) => {
                // Merge new inspections with existing ones
                return [
                  ...prevInspections.filter(
                    (i: any) => !batchIds.includes(i.id)
                  ),
                  ...res,
                ];
              });

              // Mark these inspections as loaded
              setLoadedInspectionIds((prev) => {
                const updated = new Set(prev);
                batchIds.forEach((id) => updated.add(id));
                return updated;
              });
            }

            // Add a small delay between batches to keep UI responsive
            await new Promise((resolve) => setTimeout(resolve, 50));
          }
        } catch (error) {
          console.error("Error fetching inspections:", error);
        }
      };

      fetchBatches();
    }, 0);
  };

  const fetchMoreProjects = async () => {
    const userId: any = localStorage.getItem("ScalarUserId");
    const params = {
      search: search.toLowerCase(),
      userId,
      isForFirst: false,
      lastDocument: lastDoc,
      limitValue: 10,
    };

    try {
      const projectsArray: any = await getProjects(params);
      if (projectsArray) {
        // First update the projects list immediately
        const newProjects = [...projects, ...projectsArray.list];
        setProjects(newProjects);
        setSearchedProjects(newProjects);
        setHasMany(projectsArray.hasMore);
        setLastDoc(projectsArray.lastDocument);

        // Then fetch inspections for just the new projects in the background
        // Don't await this - let it happen asynchronously
        fetchInspectionsForProjects(projectsArray.list);
      }
    } catch (error) {
      message.error(t("Something went wrong!"));
    }
  };

  const handleSearch = (value: string) => {
    setSearchLoader(true);
    setSearch(value);
    // if (value.trim() === "") {
    //   setSearchLoader(false)
    //   return
    // };
    debouncedSearchProjets(value.trim());
  };

  const debouncedSearchProjets = useCallback(
    debounce(async (value: string) => {
      try {
        setSearchLoader(true);
        const userId: any = localStorage.getItem("ScalarUserId");
        const params = {
          search: value.toLowerCase(),
          userId,
          isForFirst: true,
          lastDocument: null, // Reset pagination when searching
          limitValue: 20,
        };

        const projectsArray: any = await getProjects(params);
        if (projectsArray) {
          // Update projects first - immediately
          setProjects(projectsArray.list);
          setSearchedProjects(projectsArray.list);
          setHasMany(projectsArray.hasMore);
          setLastDoc(projectsArray.lastDocument);
          setSearchLoader(false);

          // Clear loaded inspections when new search
          setLoadedInspectionIds(new Set());

          // Fetch inspections for the new results
          fetchInspectionsForProjects(projectsArray.list);
        }
      } catch (error) {
        message.error(t("Something went wrong!"));
      } finally {
        setSearchLoader(false);
      }
    }, 500),
    []
  );

  const fetchIsnspectionTypes = async (userId: any) => {
    try {
      const res: any = await getUserReportTypes(userId);
      if (res.inspectionTypes) {
        setInspectionTypeList(res.inspectionTypes);
      } else {
        message.error(t("Something went wrong!"));
      }
    } catch (error) {
      message.error(t("Error fetching Inspection types."));
    }
  };

  const fetchIsnspectionForProgress = async () => {
    try {
      const res: any = await fetchInspectionsByIds([]);
      if (res) {
        setInspections(res);
      } else {
        message.error(t("Something went wrong!"));
      }
    } catch (error) {
      message.error(t("Something went wrong!"));
    }
  };

  const fetchProjectsInspectionTypesAndInspections = async (
    userId: string,
    firebaseUid: string
  ) => {
    try {
      setLoader(true);
      const userDetails: any = await fetchUser(firebaseUid);
      const { client }: any = userDetails;
      localStorage.setItem("client", client);
      await fetchProjects(userId);
      await fetchIsnspectionTypes(firebaseUid);
      // await fetchIsnspectionForProgress();
    } catch (error) {
      message.error(t("Something went wrong!"));
    } finally {
      setLoader(false);
    }
  };

  const firstRender = useRef<any>(false);

  useEffect(() => {
    const laguage: any = localStorage.getItem("ScalarLanguage");
    handleChangeLanguage(laguage);
    localStorage.removeItem("ViewInspectionDetails");
    const userId: any = localStorage.getItem("ScalarUserId");
    const firebaseUid: any = localStorage.getItem("ScalarFirebaseUid");

    if (!firstRender.current) {
      fetchProjectsInspectionTypesAndInspections(userId, firebaseUid);
      firstRender.current = true;
    }

    const showTermsModal = localStorage.getItem("showTermsModal");
    if (showTermsModal === "true") {
      setIsTermsModalVisible(true);
    }
    const showPrivacyModal = localStorage.getItem("showPrivacyModal");
    if (showPrivacyModal === "true") {
      setIsPrivacyModalVisible(true);
    }
  }, [refresh]);

  // useEffect(() => {
  //   const searchArray = projects.filter((item: any) => {
  //     return item.name.toLowerCase().includes(search.trim().toLowerCase());
  //   });
  //   setSearchedProjects(searchArray);
  // }, [search]);

  const handleDelete = async (e: any) => {
    e.preventDefault();
    setLoader(true);
    const res: any = await deleteProject(selectedProjects);
    if (res) {
      message.success(t("Successfully deleted!"));
      setLoader(false);
      setIsModalOpen(false);
      setRefresh(!refresh);
    } else {
      setLoader(false);
      setIsModalOpen(false);
      setRefresh(!refresh);
      message.error(t("Something went wrong, try again later!"));
    }
  };

  const handleTermsOk = async () => {
    setIsTermsModalVisible(false);
    localStorage.setItem("showTermsModal", "false");
    localStorage.setItem("showPrivacyModal", "true");
    setIsPrivacyModalVisible(true);
  };

  const handleTermsCancel = () => {
    setIsTermsModalVisible(false);
    localStorage.removeItem("showTermsModal");
    localStorage.setItem("showTermsModal", "false");
    localStorage.setItem("showPrivacyModal", "true");
    setIsPrivacyModalVisible(true);
  };

  const handlePrivacyOk = async () => {
    try {
      const firebaseUid: any = localStorage.getItem("ScalarFirebaseUid");
      const res: any = await updateUserTermsFlag(firebaseUid);
      if (res) {
        setIsPrivacyModalVisible(false);
        localStorage.setItem("showPrivacyModal", "false");
        localStorage.removeItem("showPrivacyModal");
        localStorage.removeItem("showTermsModal");
      }
    } catch (error) {
      message.error(t("Something went wrong, try again later!"));
    }
  };

  const handlePrivacyCancel = () => {
    setIsPrivacyModalVisible(false);
    localStorage.setItem("showPrivacyModal", "false");
  };

  const handleProjectClick = (id: any, name: any, inspectionTypeId: any) => {
    localStorage.setItem("ScalarProjectId", id);
    localStorage.setItem("ScalarProjectName", name);
    localStorage.setItem("ScalarInspectionTypeId", inspectionTypeId);
    router.push("/project");
  };

  const exportSelectedProjects = async () => {
    if (selectedProjects.length === 0) {
      message.info(t("Select projects to export."));
      return;
    }
    setIsExportModalOpen(true);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsActionMenuVisible(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const setTermsFlag = async () => {};

  return (
    <>
      {loader && <Loader />}
      <div className="flex h-screen">
        <Sidebar />
        <div
          className={`${
            isCollapsed ? "w-[calc(100vw-60px)]" : "w-[calc(100vw-16%)]"
          }`}
        >
          <Navbar
            search={false}
            searchInspection={search}
            setSearch={setSearch}
          />
          <div
            className={`h-[calc(100%-60px)] text-black ${OpenSans.className} pb-10`}
          >
            <div className="w-full bg-white flex justify-between items-center px-10 pt-6 pb-1 sticky top-0 z-10">
              <h1 className="text-[24px] leading-[24px] font-[500]">
                {t("Projects")}
              </h1>
              <div className="flex gap-2">
                <Button
                  onClick={() => router.push("/project/newProject")}
                  type="primary"
                  className={`${OpenSans.className} custom-button px-4 rounded-xl text-[14px] border-opacity-30 bg-[#2F80ED] text-white h-[44px] `}
                >
                  <Image
                    width={20}
                    height={20}
                    alt="logo"
                    src={"/images/home/<USER>"}
                    className="w-[20px] h-[20px]"
                  />
                  <p className={`text-white text-[14px] font-[400]`}>
                    {t("New Project")}
                  </p>
                </Button>
                <Button
                  onClick={() => setIsActionMenuVisible(!isActionMenuVisible)}
                  type="primary"
                  className={`${OpenSans.className} w-[100px] custom-button-disable px-4 rounded-xl text-[14px] border-opacity-30 bg-[#2F80ED] text-white h-[44px]`}
                  disabled={searchedProjects.length === 0}
                >
                  {t("Actions")}
                </Button>
                {isActionMenuVisible && (
                  <ActionButtons
                    setIsActionMenuVisible={setIsActionMenuVisible}
                    selectedProjects={selectedProjects}
                    setRefresh={setRefresh}
                    t={t}
                  />
                )}
              </div>
            </div>
            {/* <div className="px-10 flex justify-end items-center pt-2 pb-6 z-10 bg-white">
              <Input
                className="w-[40%] px-3 h-[35px] bg-[#2F80ED0D] border-none"
                placeholder={t("Search")}
                value={search}
                suffix={
                  <Image
                    width={12}
                    height={12}
                    className={`${
                      search !== ""
                        ? "w-[30px] relative left-2 cursor-pointer"
                        : "w-[12px]"
                    } ${search !== "" ? "h-[30px]" : "h-[12px]"}`}
                    src={
                      search.length !== 0
                        ? "/images/cancel.svg"
                        : "/images/Icon.png"
                    }
                    alt="icon"
                    onClick={
                      search !== ""
                        ? () => {
                            setSearch("");
                            debouncedSearchProjets("");
                          }
                        : () => {}
                    }
                  />
                }
                onChange={(e) => handleSearch(e.target.value)}
              />
              <div className="flex gap-2">
                <div className="relative">
                  <Button
                    onClick={() => setIsActionMenuVisible(!isActionMenuVisible)}
                    type="primary"
                    className={`${OpenSans.className} w-[100px] custom-button-disable px-4 rounded-xl text-[14px] border-opacity-30 bg-[#2F80ED] text-white h-[44px]`}
                    disabled={searchedProjects.length === 0}
                  >
                    {t("Actions")}
                  </Button>
                  {isActionMenuVisible && (
                    <ActionButtons
                      setIsActionMenuVisible={setIsActionMenuVisible}
                      selectedProjects={selectedProjects}
                      setRefresh={setRefresh}
                      t={t}
                    />
                  )}
                </div>
              </div>
            </div> */}
            {searchedProjects.length === 0 ? (
              <div className="w-full h-[calc(100vh-132px)] mt-26 flex justify-center items-center">
                {!loader && (
                  <div className="w-[352px] text-left">
                    <div className="w-full flex justify-center items-center">
                      <Image
                        src={"/images/home/<USER>"}
                        width={352}
                        height={337}
                        alt={t(
                          "Start a new inspection by clicking New Inspection"
                        )}
                        className="w-[35vh]"
                      />
                    </div>
                    <p className="text-[14px] leading-[20.8px] text-center font-[400] text-[#626d7d] mt-4">
                      {t("Start a new inspection by clicking New Inspection")}
                    </p>
                  </div>
                )}
              </div>
            ) : (
              <div
                id="projectScroll"
                className="px-10 mt-6 h-[calc(100vh-208px)] overflow-auto scrollbar"
              >
                {!loader && !searchLoader && (
                  <InfiniteScroll
                    dataLength={searchedProjects.length}
                    next={fetchMoreProjects}
                    hasMore={hasMany}
                    loader={
                      <div className="w-full flex justify-center items-center gap-2 mb-6">
                        <Spin
                          size="default"
                          indicator={<LoadingOutlined spin />}
                        />
                        {t("Loading")}...
                      </div>
                    }
                    scrollThreshold={0.8}
                    scrollableTarget="projectScroll"
                  >
                    <ProjectTable
                      {...{
                        searchedProjects,
                        inspectionTypeList,
                        inspections,
                        setDeleteId,
                        setIsModalOpen,
                        handleProjectClick,
                        selectedProjects,
                        setSelectedProjects,
                        loadedInspectionIds,
                      }}
                    />
                  </InfiniteScroll>
                )}
                {searchLoader && (
                  <div className="w-full h-full flex justify-center items-center gap-2 mb-4">
                    <Spin size="large" indicator={<LoadingOutlined spin />} />
                    {t("Searching")}...
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
        <TNCModal
          {...{
            isTermsModalVisible,
            handleTermsOk,
            handleTermsCancel,
            t,
          }}
        />
        <PrivacyModal
          {...{
            isPrivacyModalVisible,
            handlePrivacyOk,
            handlePrivacyCancel,
            setTermsFlag,
            t,
          }}
        />
      </div>
    </>
  );
};

export default Home;
