import axios from "axios";
import { message } from "antd";
import {
  getStorage,
  ref,
  uploadBytes,
  getDownloadURL,
  deleteObject,
} from "firebase/storage";
import { auth, db, storage } from "@/firebase.config";
import {
  collection,
  addDoc,
  doc,
  getDoc,
  updateDoc,
  query,
  where,
  getDocs,
} from "firebase/firestore";
import imageCompression from "browser-image-compression";
import {
  suggestionsRes,
  measure,
  findingRes,
  multipickerRes,
} from "@/components/newFindings/newResponse";
import { v4 as uuidv4 } from "uuid";
import { fetch } from "../libs/helpers";
import { createDBFieldObj, fetchInspectionAndMapFields, mapFiledsWithValue } from "./fieldsMapping";
import { fetchInspection } from "./inspectionDetails.api";

const host = process.env.NEXT_HOST;

export const fetchReportTypeId = async (inspectionId: any) => {
  try {
    const docRef = doc(db, "inspection", inspectionId);

    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      const doc = await docSnap.data();
      return await doc.reporttype.id;
    } else {
      return false;
    }
  } catch (error) {
    return false;
  }
};

export const fetchFindingTypeId = async (reporttypeId: any) => {
  try {
    const collectionRef = collection(db, "finding_type");
    const reportTypeRef = doc(db, "report_type", reporttypeId);

    const condition = query(
      collectionRef,
      where("report_type", "==", reportTypeRef)
    );

    const querySnapshot = await getDocs(condition);

    const documents = querySnapshot.docs.map((doc: any) => ({
      id: doc.id,
    }));
    return documents;
  } catch (error) {
    return false;
  }
};

export const generateUrls = async (files: any) => {
  const urls = [];
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    const storage = getStorage();
    const storageRef = ref(storage, `uploads/finding/${file.name}`);

    try {
      const snapshot = await uploadBytes(storageRef, file);
      const downloadURL = await getDownloadURL(snapshot.ref);

      urls.push(downloadURL);
    } catch (error) { }
  }
  return urls;
};

// Extract file names from URLs
const extractFileName = (url: string) => {
  const decodedUrl = decodeURIComponent(url);
  const parts = decodedUrl.split("/");
  const fileNameWithToken = parts[parts.length - 1];
  const fileName = fileNameWithToken.split("?")[0];
  return fileName;
};

export const generateFindings = async (
  images: any,
  videos: any,
  setLoader: any,
  inspection_id: any,
  findingTypeId: any,
  reportType: any,
  cancelToken: any,
  setIsLoadingCard: any,
  setTitleFields: any,
  t: any
) => {
  const idToken = localStorage.getItem("ScalarIdToken");
  const parentFindingId = sessionStorage.getItem("parent_finding_id");

  // if (images.length === 0) {
  //   message.error(t("Image is required to generate findings."));
  //   return false;
  // }

  const formData = new FormData();
  setIsLoadingCard(true);

  // Retrieve existing media from localStorage
  const existingMedia = JSON.parse(
    localStorage.getItem("resize_media") || "[]"
  );

  // Separate existing images and videos
  const existingImages = existingMedia
    .filter((media: any) => !media.isVideo)
    .map((media: any) => extractFileName(media.url));

  const existingVideos = existingMedia
    .filter((media: any) => media.isVideo)
    .map((media: any) => extractFileName(media.url));

  // Filter out images that are already in localStorage
  const newImages = images.filter(
    (image: any) => !existingImages.includes(image.name)
  );
  const newVideos = videos.filter(
    (video: any) => !existingVideos.includes(video.name)
  );

  // Identify images and videos in existing but not in the current arrays
  const removedImageUrls = existingMedia
    .filter(
      (media: any) =>
        !media.isVideo &&
        !images.some((image: any) => extractFileName(media.url) === image.name)
    )
    .map((media: any) => media.url);

  const removedVideoUrls = existingMedia
    .filter(
      (media: any) =>
        media.isVideo &&
        !videos.some((video: any) => extractFileName(media.url) === video.name)
    )
    .map((media: any) => media.url);

  const allRemovedUrls = [...removedImageUrls, ...removedVideoUrls];
  if (allRemovedUrls.length > 0) {
    deleteFilesByUrls(allRemovedUrls);
  }

  // Remove removed URLs from existing media
  const updatedExistingMedia = existingMedia.filter(
    (media: any) => !allRemovedUrls.includes(media.url)
  );

  // Compress new images
  const compressedImages = await Promise.all(
    newImages.map(async (image: any) => {
      const options = {
        maxSizeMB: 1,
        maxWidthOrHeight: 512,
        useWebWorker: true,
      };

      try {
        const compressedImage = await imageCompression(image, options);
        return compressedImage;
      } catch (error) {
        throw error;
      }
    })
  );

  // Generate URLs for new images and videos
  const newImageUrlArray = await generateUrls(compressedImages);
  const newVideoUrlArray =
    newVideos.length > 0 ? await generateUrls(newVideos) : [];

  // Combine existing and new media
  const imageUrlArray = [
    ...updatedExistingMedia
      .filter((media: any) => !media.isVideo)
      .map((media: any) => media.url),
    ...newImageUrlArray,
  ];
  const videoUrlArray = [
    ...updatedExistingMedia
      .filter((media: any) => media.isVideo)
      .map((media: any) => media.url),
    ...newVideoUrlArray,
  ];

  // Create array for storing media with flags
  const mediaArray = imageUrlArray.map((url) => ({ isVideo: false, url }));
  videoUrlArray.forEach((url: any) => mediaArray.push({ isVideo: true, url }));

  // Store the updated media array in localStorage
  localStorage.setItem("resize_media", JSON.stringify(mediaArray));

  const rowData = {
    image_urls: imageUrlArray,
    ...(videoUrlArray.length > 0 && { video_urls: videoUrlArray }),
    ...(parentFindingId !== "null" && { parent_finding: parentFindingId }),
  };

  const response: any = await fetch({
    url: `${host}/v4/inspection_types/${reportType}/inspections/${inspection_id}/finding_types/${findingTypeId}/suggestions`,
    method: "POST",
    data: rowData,
    cancelToken
  });

  // const res = multipickerRes
  // console.log('res', res)
  setTitleFields(response?.suggestions?.title_fields);
  return response;
};

export const generateFindingsWithoutImages = async (
  setLoader: any,
  inspection_id: any,
  findingTypeId: any,
  inspectionType: any,
  cancelToken: any,
  setIsLoadingCard: any,
  setTitleFields: any,
  t: any
) => {
  const idToken = localStorage.getItem("ScalarIdToken");
  const parentFindingId = sessionStorage.getItem("parent_finding_id");

  const response: any = await fetch({
    url: `${host}/inspection_types/${inspectionType}/findings/${findingTypeId}/template`,
    method: "POST",
    data: {
      image_urls: [],
    },
    cancelToken,
  });

  setIsLoadingCard(false);
  setTitleFields(response?.suggestions?.title_fields);
  return response;
};

export const fetchTextSuggestions = async (
  inspectionTypeId: any,
  inspectionId: any,
  findingTypeId: any,
  payload: any
) => {

  const response: any = await fetch({
    url: `${host}/v4/inspection_types/${inspectionTypeId}/inspections/${inspectionId}/finding_types/${findingTypeId}/text_suggestions`,
    method: "POST",
    data: payload,
  });

  return response;
};

export const updateFindings = async (
  setLoader: any,
  reportTypeId: any,
  findingTypeId: any,
  cancelToken: any,
  setIsLoadingCard: any
) => {
  const idToken = localStorage.getItem("ScalarIdToken");
  setIsLoadingCard(true);

  const response: any = await fetch({
    url: `${host}/reports/${reportTypeId}/${findingTypeId}/inspect_suggestions_dummy`,
    method: "POST",
    cancelToken,
  });

  setIsLoadingCard(false);
  return response.suggestions.fields;
};

// `${host}/inspections/${inspection_id}/${findingTypeId}/suggestions` -------- New Url
export const generateMeasureFindings = async (
  setLoader: any,
  inspection_id: any,
  findingTypeId: any,
  reportType: any,
  rowData: any,
  cancelToken: any,
  setIsLoadingCard: any,
  setTitleFields: any
) => {
  const idToken = localStorage.getItem("ScalarIdToken");
  setIsLoadingCard(true);

  const response: any = await fetch({
    url: `${host}/v4/inspection_types/${reportType}/inspections/${inspection_id}/finding_types/${findingTypeId}/suggestions`,
    method: "POST",
    data: rowData,
    cancelToken,
  });

  setIsLoadingCard(false);
  setTitleFields(response.suggestions.title_fields);
  return response;

  const docRef = doc(db, "inspection", inspection_id);
  const docSnap = await getDoc(docRef);

  if (docSnap.exists()) {
    const doc = await docSnap.data();
    if (doc) {
      setIsLoadingCard(false);
      const parsedMeasureSuggestion = JSON.parse(doc?.measure_fields)
      setTitleFields(parsedMeasureSuggestion?.title_fields);

      const mappedFields = mapFiledsWithValue(parsedMeasureSuggestion?.fields, response?.suggestions?.fields)
      parsedMeasureSuggestion.fields = mappedFields;

      return { suggestions: parsedMeasureSuggestion };
    }
  } else {
    throw new Error("Inspection not found.");
  }

  setIsLoadingCard(false);
  setTitleFields(response.suggestions.title_fields);
  return response;
  // setTitleFields(measure.suggestions.title_fields)
  // return measure.suggestions.fields;
};

export const handleUpload = async (files: any, mediaArray: any) => {
  const urls = [];
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    const mediaUrlObj = mediaArray.find((media: any) => {
      const mediaFileName = extractFileName(media.url);
      return mediaFileName === file.name;
    });
    const fileExtension = file.name.split(".").pop();

    // Generate new filename based on type and timestamp
    const newFileName = file.type.startsWith("video/")
      ? `video_${uuidv4()}.${fileExtension}`
      : `image_${uuidv4()}.${fileExtension}`;

    const storage = getStorage();
    const storageRef = ref(storage, `uploads/finding/${newFileName}`);

    try {
      const snapshot = await uploadBytes(storageRef, file);
      const downloadURL = await getDownloadURL(snapshot.ref);

      if (file.type.startsWith("video/")) {
        urls.push({
          isVideo: true,
          url: downloadURL,
          resize_url: mediaUrlObj.url,
        });
      } else {
        urls.push({
          isVideo: false,
          url: downloadURL,
          resize_url: mediaUrlObj.url,
        });
      }
    } catch (error) { }
  }
  return urls;
};

export const handleUploadWhileEdit = async (mediaArray: any) => {
  const storage = getStorage();
  const maxRetries = 2; // Number of retry attempts
  const uploadedUrls: string[] = []; // Array to store URLs of successfully uploaded files

  const uploadFile = async (
    file: any,
    isVideo: boolean,
    attempt = 0
  ): Promise<any> => {
    const fileExtension = file.name.split(".").pop();
    const newFileName = `${isVideo ? "video" : "image"
      }_${uuidv4()}.${fileExtension}`;
    const storageRef = ref(storage, `uploads/finding/${newFileName}`);

    try {
      const snapshot = await uploadBytes(storageRef, file);
      const downloadURL = await getDownloadURL(snapshot.ref);
      uploadedUrls.push(downloadURL); // Store the URL of the uploaded file
      return { isVideo, url: downloadURL, resize_url: downloadURL };
    } catch (error) {
      console.error(
        `Failed to upload ${isVideo ? "video" : "image"} on attempt ${attempt + 1
        }:`,
        error
      );
      if (attempt < maxRetries) {
        return uploadFile(file, isVideo, attempt + 1);
      } else {
        // Delete all successfully uploaded files if any upload fails after max retries
        deleteFilesByUrls(uploadedUrls);
        throw new Error(
          `Failed to upload ${isVideo ? "video" : "image"} after ${maxRetries + 1
          } attempts`
        );
      }
    }
  };

  const uploadImage = async (file: any) => {
    const optionsFor1280 = { maxWidthOrHeight: 1280, useWebWorker: true };
    const optionsFor512 = { maxWidthOrHeight: 512, useWebWorker: true };

    try {
      const compressedImage1280 = await imageCompression(file, optionsFor1280);
      const compressedImage512 = await imageCompression(file, optionsFor512);

      const [url1280, url512] = await Promise.all([
        uploadFile(compressedImage1280, false),
        uploadFile(compressedImage512, false),
      ]);

      return { isVideo: false, url: url1280.url, resize_url: url512.url };
    } catch (error) {
      console.error("Failed to compress or upload image:", error);
      throw error;
    }
  };

  const uploadPromises = mediaArray.map((file: any) => {
    return file.type.startsWith("video/")
      ? uploadFile(file, true)
      : uploadImage(file);
  });

  try {
    return await Promise.all(uploadPromises);
  } catch (error) {
    console.error("Error in handleUploadWhileEdit:", error);
    return [];
  }
};

export const addFindingDetails = async (
  inspection_id: any,
  findingDetails: any,
  findingTypeId: any
) => {
  // inspection_id
  try {
    // console.log('findingDetails', findingDetails)
    const inspectionRef = doc(db, "inspection", inspection_id);
    const findingTypeRef = doc(db, "finding_type", findingTypeId);
    findingDetails.inspection = inspectionRef;
    findingDetails.finding_type = findingTypeRef;
    const finding = await addDoc(collection(db, "finding"), findingDetails);
    if (finding.id) {
      return finding;
    } else {
      return false;
    }
  } catch (e) {
    console.log('e', e)
    return false;
  }
};

export const fetchFinding = async (findingId: any, setIsMeasureAvailable: any = (value: boolean) => {}) => {
  try {
    const docRef = doc(db, "finding", findingId);

    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      const docData = await docSnap.data();
      const doc: any = { id: findingId, ...docData };

      const inspectionId: any = localStorage.getItem("ScalarInspectionId");
      const inspectionDetails: any = await fetchInspection(inspectionId);
      if (inspectionDetails?.measure_fields) {
        const measureFields = JSON.parse(inspectionDetails?.measure_fields)?.fields;
        setIsMeasureAvailable(measureFields?.length > 0);
      }

      return doc;

      const mappedFields = await fetchInspectionAndMapFields(
        doc?.fields,
        doc?.finding_type?.id
      )
      setIsMeasureAvailable(mappedFields?.isMeasureAvailable)
      return { ...doc, fields: JSON.stringify(mappedFields?.fields)};
    } else {
      return false;
    }
  } catch (error) {
    console.log('error', error)
    return false;
  }
};

export const updateFindingDetails = async (
  findingDetails: any,
  documentId: string
) => {
  try {
    
    // if (findingDetails.fields) {
    //   // Ensure fields is a string
    //   findingDetails.fields = createDBFieldObj(JSON.parse(findingDetails.fields))
    // }

    const docRef = doc(db, "finding", documentId);
    await updateDoc(docRef, findingDetails);
    return true;
  } catch (e) {
    return false;
  }
};

const getFilePathFromUrl = (url: string) => {
  try {
    // Handle null or undefined URLs
    if (!url) return null;

    const urlObj = new URL(url);
    const path = urlObj.pathname.split("/o/")[1];
    if (!path) return null;

    return decodeURIComponent(path);
  } catch (error) {
    console.error("Error parsing URL:", url, error);
    return null;
  }
};

export const deleteFiles = async (
  fileObjects: {
    mediaIndex: number;
    isVideo: boolean;
    url: string;
    resize_url: string;
  }[]
) => {
  const maxRetries = 3; // Maximum number of retry attempts

  const deleteFileWithRetry = async (
    fileRef: any,
    attempt = 0
  ): Promise<void> => {
    try {
      await deleteObject(fileRef);
    } catch (error) {
      console.warn(
        `Failed to delete file: ${fileRef} on attempt ${attempt + 1}`,
        error
      );
      if (attempt < maxRetries) {
        await deleteFileWithRetry(fileRef, attempt + 1);
      } else {
        console.error(
          `Failed to delete file: ${fileRef} after ${maxRetries} attempts`
        );
      }
    }
  };

  try {
    const deletePromises = fileObjects.map((media) => {
      const fileRefs = [];

      if (media.isVideo) {
        const videoFilePath = getFilePathFromUrl(media.url);
        if (videoFilePath) {
          fileRefs.push(ref(storage, videoFilePath));
        }
      } else {
        const imageFilePath = getFilePathFromUrl(media.url);
        if (imageFilePath) {
          fileRefs.push(ref(storage, imageFilePath));
        }
        const resizeImageFilePath = getFilePathFromUrl(media.resize_url);
        if (resizeImageFilePath) {
          fileRefs.push(ref(storage, resizeImageFilePath));
        }
      }

      return Promise.all(
        fileRefs.map((fileRef) => deleteFileWithRetry(fileRef))
      );
    });

    await Promise.all(deletePromises);
    return true;
  } catch (error) {
    console.error("Error in deleteFiles:", error);
    throw error;
  }
};

export const deleteFilesByUrls = async (urls: string[]) => {
  try {
    const deletePromises = urls.map((url) => {
      const filePath = getFilePathFromUrl(url);
      if (filePath) {
        const fileRef = ref(storage, filePath);
        return deleteObject(fileRef).catch((error) => {
          console.warn(`Failed to delete file: ${filePath}`, error);
        });
      }
      return Promise.resolve();
    });

    await Promise.allSettled(deletePromises);
    return true;
  } catch (error) {
    console.error("Error in deleteFilesByUrls:", error);
    throw error;
  }
};

export const fetchAPIResponse = async (
  inspectionType: any,
  inspection_id: any,
  findingTypeId: any,
  payload: any,
  fieldId: any
) => {
  const idToken = localStorage.getItem("ScalarIdToken");

  const response: any = await fetch({
    url: `${host}/v4/inspection_types/${inspectionType}/inspections/${inspection_id}/finding_types/${findingTypeId}/api_fields/${fieldId}/calculate`,
    method: "POST",
    data: payload,
  });

  return response;
};

export const fetchParentAPIValue = async (
  reportType: any,
  inspection_id: any,
  findingTypeId: any,
  apiFieldId: any,
  payload: any
) => {
  const idToken = localStorage.getItem("ScalarIdToken");

  const response: any = await fetch({
    url: `${host}/v4/inspection_types/${reportType}/inspections/${inspection_id}/finding_types/${findingTypeId}/parent_api_fields/${apiFieldId}/calculate`,
    method: "POST",
    data: payload,
  });

  return response;
};
