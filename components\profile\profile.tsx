"use client";
import React, { useEffect, useState } from "react";
import Navbar from "@/components/Navbar/navbar";
import Sidebar from "@/components/sidebar/sidebar";
import { Roboto, Poppins, Open_Sans } from "next/font/google";
import Image from "next/image";
import { languageOptions } from "@/src/libs/constants";
import { Select, Avatar, Input, Modal, message, Button } from "antd";
import {
  fetchUserDetails,
  generateProfileFields,
  getUserProfile,
  updateProfileDetails,
} from "@/src/services/profile.api";
import { useLoaderContext } from "@/context/LoaderContext";
import Loader from "@/components/loader/loader";
import { useTranslation } from "react-i18next";
import {
  handleChangeLanguage,
  useLanguageContext,
} from "@/context/LanguageContext";
import PrivacyModal from "@/components/profile/privacyModal";
import TermsModal from "@/components/profile/tncModal";
import { useSidebarContext } from "@/context/sidebarContext";
import ProfileFields from "./profileFields";
import { Timestamp } from "firebase/firestore";

const { Option } = Select;

const poppins = Poppins({
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
});
const roboto = Roboto({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});
const OpenSans = Open_Sans({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

const Profile = () => {
  const { t } = useTranslation();
  const { loader, setLoader } = useLoaderContext();
  const { isCollapsed } = useSidebarContext();
  const [selectedLanguage, setSelectedLanguage] = useState("");
  const [isTermsModalVisible, setIsTermsModalVisible] = useState(false);
  const [isPrivacyModalVisible, setIsPrivacyModalVisible] = useState(false);
  const [searchInspection, setSearchInspection] = useState<any>("");
  const [userdetails, setUserdetails] = useState<any>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const [isEdit, setIsEdit] = useState(false);
  const [profileFields, setProfileFields] = useState<any>(null);
  const [profileFieldLoader, setProfileFieldLoader] = useState(false);
  const [suggestionResponse, setSuggestionResponse] = useState<any>(null);
  const [textSuggestionLoader, setTextSuggestionLoader] = useState(false);
  const [fieldImageUrl, setFieldImageUrl] = useState<any>("");
  const [fieldImage, setFieldImage] = useState<any>(null);

  const { setIsLanguageChange } = useLanguageContext();

  const fetchUser = async () => {
    setLoader(true);
    const name = localStorage.getItem("ScalarUserName");
    const email = localStorage.getItem("ScalarUserEmail");
    const userId = localStorage.getItem("ScalarUserId");
    const user = {
      name: name,
      email: email,
    };
    setUserdetails(user);
    try {
      const details = await getUserProfile(userId);
      if (details && details?.suggestion) {
        setLoader(false);
        console.log("details", details);
        setSuggestionResponse(details?.suggestion);
        setProfileFields(details?.suggestion?.fields);
        setIsEdit(true);
      } else {
        setProfileFieldLoader(true);
        const res = await generateProfileFields();
        if (res) {
          console.log('res', res)
          setSuggestionResponse(res);
          setProfileFields(res?.fields);
        }
      }
    } catch (error) {
      message.error(t("Something went wrong!"));
    } finally {
      setProfileFieldLoader(false);
    }
    setLoader(false);
  };

  useEffect(() => {
    fetchUser();
    const laguage: any = localStorage.getItem("ScalarLanguage");
    setSelectedLanguage(laguage);
    handleChangeLanguage(laguage);
  }, []);

  const getTextSuggestions = async (suggestionObj: any) => {
    // const inspectionTypeId: any = localStorage.getItem("reportTypeId");
    // const inspectionId: any = localStorage.getItem("ScalarInspectionId");
    // const findingTypeId: any = sessionStorage.getItem("FindingTypeId");
    // try {
    //   setTextSuggestionLoader(true);
    //   const res = await fetchProfileTextSuggestions(
    //     inspectionTypeId,
    //     inspectionId,
    //     findingTypeId,
    //     suggestionObj
    //   );
    //   if (res) {
    //     const textNumberFields = res?.suggestions?.fields.filter(
    //       (field: any) => field.type === "text" || field.type === "number"
    //     );
    //     const fieldsAfterUpdate = suggestionObj.suggestions.fields.map(
    //       (field: any) => {
    //         const textNumField = textNumberFields.find(
    //           (f: any) => f.id === field.id
    //         );
    //         if (textNumField) {
    //           return {
    //             ...field,
    //             suggested_value: textNumField.suggested_value,
    //           };
    //         } else {
    //           return field;
    //         }
    //       }
    //     );
    //     setSuggestionResponse(res);
    //     setProfileFields(fieldsAfterUpdate);
    //   }
    // } catch (error) {
    //   message.error(t("Failed to get text suggestions"));
    // } finally {
    //   setTextSuggestionLoader(false);
    // }
  };

  const handleTermsClick = () => {
    setIsTermsModalVisible(true);
  };

  const handlePrivacyClick = () => {
    setIsPrivacyModalVisible(true);
  };

  const handleTermsOk = () => {
    setIsTermsModalVisible(false);
    setIsPrivacyModalVisible(true);
  };

  const handleTermsCancel = () => {
    setIsTermsModalVisible(false);
  };

  const handlePrivacyOk = () => {
    setIsPrivacyModalVisible(false);
  };

  const handlePrivacyCancel = () => {
    setIsPrivacyModalVisible(false);
  };

  const handleSubmit = async () => {
    try {
      handleChangeLanguage(selectedLanguage);
      setIsLanguageChange((prev: boolean) => !prev);
      const filteredTypeFields = profileFields.filter(
        (obj: any) => obj.required
      );
      for (const field of filteredTypeFields) {
        if (field.suggested_value === null) {
          message.error(t("Please fill up all the required fields."));
          return; // Stop execution of handleSubmit if validation fails
        }
      }

      setLoader(true);
      const userId: any = localStorage.getItem("ScalarUserId");
      if (profileFields && profileFields.length > 0) {
        const profileDetails: any = {
          suggestion: { ...suggestionResponse, fields: profileFields },
          updated_at: Timestamp.now(),
        };

        await updateProfileDetails(profileDetails, userId);
      }

      setIsEdit(true);
      message.success(t("Successfully Updated!"));
    } catch (error) {
      message.error(t("Something went wrong, try again later!"));
    } finally {
      setLoader(false);
    }
  };

  return (
    <>
      {loader && <Loader />}
      <div className="flex h-screen">
        <Sidebar />
        <div
          className={`${
            isCollapsed ? "w-[calc(100vw-60px)]" : "w-[calc(100vw-16%)]"
          }`}
        >
          <Navbar
            search={false}
            searchInspection={searchInspection}
            setSearch={setSearchInspection}
          />
          <div
            className={` h-[calc(100%-60px)] px-10 text-black ${poppins.className} overflow-auto scrollbar`}
          >
            <div className="w-full flex justify-between pt-6 pb-4 bg-white sticky top-0 z-10">
              <h1 className="text-[24px] leading-[24px] font-[500] pt-[10px]">
                {t("Profile")}
              </h1>
              <Button
                type="primary"
                className={`w-[100px] h-[44px] custom-button-disable text-[14px] leading-[14px] font-[500] bg-[#2F80ED] text-white flex items-center gap-2 p-3 rounded-xl`}
                onClick={handleSubmit}
              >
                {isEdit ? <>{t("Update")}</> : <>{t("Save")}</>}
              </Button>
            </div>
            <div className="w-full flex items-start text-black mt-4 mb-16">
              <div className="w-[15.5%]">
                <div className="flex justify-center mt-4">
                  <Avatar
                    style={{
                      backgroundColor: "#2F80ED",
                      // verticalAlign: "middle",
                      width: "150px",
                      height: "150px",
                      fontSize: "60px",
                    }}
                    size="large"
                    className={`text-[60px] leading-[20px] font-[700] ${roboto.className}`}
                  >
                    {userdetails?.name !== "null"
                      ? userdetails?.name?.slice(0, 1).toUpperCase()
                      : userdetails?.email?.slice(0, 1).toUpperCase()}
                  </Avatar>
                </div>
                <h1 className="text-[22px] mt-[10px] text-center">
                  {userdetails?.name !== "null" ? userdetails?.name : ""}
                </h1>
              </div>
              {/* <div className="w-[75%] pl-16 items-center">
                <div>
                  <button
                    className="leading-[14px] w-[450px] text-black flex justify-between items-center gap-2 mt-8 mb-2 py-3 px-2 rounded-[10px]"
                    onClick={handleTermsClick}
                  >
                    <h1
                      className={`flex justify-start pl-2 text-[14px] font-[400] ${OpenSans.className}`}
                    >
                      {t("Terms and Conditions")}
                    </h1>
                    <img
                      width={22}
                      height={22}
                      alt="logo"
                      src="/images/profile/rightErow.svg"
                      className="w-[22px] h-[22px] flex justify-end"
                    />
                  </button>
                  <button
                    className="leading-[14px] w-[450px] text-black flex justify-between items-center gap-2 mb-8 py-3 px-2 rounded-[10px]"
                    onClick={handlePrivacyClick}
                  >
                    <h1
                      className={`flex justify-start pl-2 text-[14px] font-[400] ${OpenSans.className}`}
                    >
                      {t("Privacy Policy")}
                    </h1>
                    <img
                      width={22}
                      height={22}
                      alt="logo"
                      src="/images/profile/rightErow.svg"
                      className="w-[22px] h-[22px]"
                    />
                  </button>
                  <Button
                    onClick={() => {
                      handleChangeLanguage(selectedLanguage);
                      setIsLanguageChange((prev: boolean) => !prev);
                    }}
                    type="primary"
                    className={`${poppins.className} custom-button w-[450px] rounded-xl text-[14px] border-[#B4B4B4] border-opacity-30 bg-[#2F80ED] text-white h-[35px]`}
                  >
                    <p className={`text-white text-[14px] font-[400]`}>
                      {t("Save")}
                    </p>
                  </Button>
                </div>
              </div> */}
            </div>

            <div className={`w-full px-2 md:mt-6 flex gap-4`}>
              {/* Labels */}
              <div className="w-[15.5%] flex flex-col gap-8 items-start text-center text-[14px]">
                <label className="font-[500] mt-4">{t("Name")}</label>

                <label className="font-[500] mt-2">{t("Email")}</label>

                <label className="font-[500] mt-2">{t("Language")}</label>
              </div>

              {/* Vertical Divider */}
              <div className="border-r-2 border-gray-300 ml-4" />

              {/* Inputs */}
              <div className="w-[84.5%] flex flex-col">
                {/* Name */}
                <div
                  className="border-b border-gray-200 py-4 flex"
                  style={{ borderBottomWidth: "2px" }}
                >
                  <Button
                    shape="round"
                    className={`h-[25px] cursor-default text-[12px] font-[400] !bg-blue-500 !text-white ${OpenSans.className}`}
                  >
                    {userdetails?.name !== "null" ? (
                      userdetails?.name
                    ) : (
                      <p className="text-[14px]">-</p>
                    )}
                  </Button>
                </div>

                {/* Email */}
                <div
                  className="border-b border-gray-200 py-4 flex"
                  style={{ borderBottomWidth: "2px" }}
                >
                  <Button
                    shape="round"
                    className={`h-[25px] cursor-default text-[12px] font-[400] !bg-blue-500 !text-white ${OpenSans.className}`}
                  >
                    {userdetails?.email ? (
                      userdetails?.email
                    ) : (
                      <p className="text-[14px]">-</p>
                    )}
                  </Button>
                </div>

                {/* Language */}
                <div
                  className={`${profileFields?.length > 0 && "border-b"} border-gray-200 py-4`}
                  style={{ borderBottomWidth: profileFields?.length > 0 ? "2px" : "0px" }}
                >
                  <Select
                    value={selectedLanguage}
                    onChange={(value) => setSelectedLanguage(value)}
                    placeholder="Select language"
                    className={`w-full h-[30px] rounded-[10px] font-[400] ${OpenSans.className}`}
                    suffixIcon={
                      <img
                        src={
                          isDropdownOpen
                            ? "/images/newInspection/arrow-up.svg"
                            : "/images/newInspection/arrow-down.svg"
                        }
                        alt="dropdown icon"
                      />
                    }
                    onDropdownVisibleChange={(open) => setIsDropdownOpen(open)}
                    // style={{ boxShadow: "0 0 5px 5px rgba(0, 0, 0, 0.04)" }}
                  >
                    {languageOptions.map((option) => (
                      <Option key={option.value} value={option.value}>
                        <p className="text-[12px] font-[400] pl-1">
                          {option.label}
                        </p>
                      </Option>
                    ))}
                  </Select>
                </div>
              </div>
            </div>

            {profileFields &&
            <div className="w-full text-[14px]">
              {profileFieldLoader ? (
                // <LoadingCards />
                <div className="space-y-4 animate-pulse mb-4 px-2">
                  {/* Each field row */}
                  {[...Array(1)].map((_, i) => (
                    <div key={i} className="flex items-center gap-0 mt-6">
                      <div className="flex-1 h-[50px] bg-gray-100 rounded-xl"></div>
                    </div>
                  ))}
                </div>
              ) : (
                <>
                  <ProfileFields
                    {...{
                      profileFields,
                      setProfileFields,
                      isEdit,
                      t,
                      fieldImage,
                      setFieldImage,
                      fieldImageUrl,
                      setFieldImageUrl,
                      getTextSuggestions,
                      suggestionResponse,
                      textSuggestionLoader,
                    }}
                  />
                </>
              )}
            </div>}

            <div className="flex flex-col justify-center my-8">
              <button
                className="leading-[14px] text-black flex justify-between items-center gap-2 py-3 rounded-[10px]"
                onClick={handleTermsClick}
              >
                <h1
                  className={`flex justify-start pl-2 text-[14px] font-[500] ${OpenSans.className}`}
                >
                  • {t("Terms and Conditions")}
                </h1>
                {/* <img
                      width={22}
                      height={22}
                      alt="logo"
                      src="/images/profile/rightErow.svg"
                      className="w-[22px] h-[22px] flex justify-end"
                    /> */}
              </button>
              <button
                className="leading-[14px] text-black flex justify-between items-center gap-2 py-3 rounded-[10px]"
                onClick={handlePrivacyClick}
              >
                <h1
                  className={`flex justify-start pl-2 text-[14px] font-[500] ${OpenSans.className}`}
                >
                  • {t("Privacy Policy")}
                </h1>
                {/* <img
                      width={22}
                      height={22}
                      alt="logo"
                      src="/images/profile/rightErow.svg"
                      className="w-[22px] h-[22px]"
                    /> */}
              </button>
            </div>
          </div>
          <TermsModal
            isTermsModalVisible={isTermsModalVisible}
            handleTermsOk={handleTermsOk}
            handleTermsCancel={handleTermsCancel}
            t={t}
          />
          <PrivacyModal
            isPrivacyModalVisible={isPrivacyModalVisible}
            handlePrivacyOk={handlePrivacyOk}
            handlePrivacyCancel={handlePrivacyCancel}
            t={t}
          />
        </div>
      </div>
    </>
  );
};

export default Profile;
