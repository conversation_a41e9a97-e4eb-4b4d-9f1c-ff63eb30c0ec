import React from "react";
import { Open_Sans } from "next/font/google";
import { useTranslation } from "react-i18next";

const OpenSans = Open_Sans({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

const Modal = ({ handleDelete, setDeleteId, setIsModalOpen }: any) => {
  const { t } = useTranslation();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-10 animate-fadeIn">
      <form
        className={`text-center bg-white px-8 pt-4 pb-6 rounded-xl flex justify-center items-center text-black ${OpenSans.className} animate-slideIn relative`}
        onSubmit={handleDelete}
      >
        <button
          type="button"
          className="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center absolute top-4 right-4"
          onClick={() => {
            setDeleteId("");
            setIsModalOpen(false);
          }}
        >
          <svg
            className="w-3 h-3"
            aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 14 14"
          >
            <path
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
            />
          </svg>
          <span className="sr-only">Close modal</span>
        </button>
        <div>
          <h1 className="mt-6 text-left text-[25px] leading-[52.08px] font-[600]">
            {t("Delete Inspection!")}
          </h1>
          <p className="text-[18px] text-left">
            {t("Are you sure you want to")} <br />
            {t("delete Inspection?")}
          </p>
          <div>
            <div className="text-center flex justify-between gap-2 mt-6">
              <button
                type="button"
                className="w-[50%] h-[45px] text-[14px] text-[#FF9200] border border-[#FF9200] rounded-lg bg-[#ff910020] hover:bg-[#ff910015] transition-all"
                onClick={() => {
                  setDeleteId("");
                  setIsModalOpen(false);
                }}
              >
                {t("Cancel")}
              </button>
              <button
                type="submit"
                className="w-[50%] h-[45px] text-[14px] text-white border border-[#FF9200] rounded-lg bg-[#FF9200] hover:bg-[#ff9100d0] transition-all"
              >
                {t("Delete")}
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};

export default Modal;
