'use client';

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Open_Sans } from 'next/font/google';
import { CompleteInspections } from '@/src/services/newInspection.api';
import { Modal, Button, message } from 'antd';

const OpenSans = Open_Sans({ weight: '400', subsets: ['latin'] });

interface MarkCompleteModalProps {
  isModalOpen: boolean;
  setSearchedInspections: any;
  setIsModalOpen: (open: boolean) => void;
  setMenu: (open: boolean) => void;
  selectedInspections: string[];
  setRefresh: React.Dispatch<React.SetStateAction<boolean>>;
}

const MarkCompleteModal: React.FC<MarkCompleteModalProps> = ({
  isModalOpen,
  setIsModalOpen,
  selectedInspections,
  setRefresh,
  setMenu,
  setSearchedInspections
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);

  const markCompleteInspections = async () => {
    try {
      setLoading(true);

      if (selectedInspections.length > 0) {
        const res = await CompleteInspections(selectedInspections);

        if (res) {
          message.success(t('Successfully completed all inspections.'));
        } else {
          message.warning(t('Some inspections failed to complete.'));
        }

        setSearchedInspections((prev: any) => prev.map((inspection: any) => {
          if (selectedInspections.includes(inspection.id)) {
            return {
              ...inspection,
              isCompleted: true
            }
          } else return inspection
        }))
        // setRefresh((prev) => !prev);
      }
    } catch (error) {
      message.error(t('Something went wrong, try again later!'));
    } finally {
      setLoading(false);
      setIsModalOpen(false);
      setMenu(false)
    }
  };

  return (
    <Modal
      open={isModalOpen}
      onCancel={() => setIsModalOpen(false)}
      footer={null}
      centered
      className={`${OpenSans.className} custom-complete-modal`}
      width={350}
    >
      <div className="relative">

        <h1 className="mt-6 text-left text-[25px] leading-[52.08px] font-[600]">
          {t('Complete')} {t('Inspections')}!
        </h1>

        <p className="text-[18px] text-left mt-2">
          {t('Are you sure you want to mark')} <br />
          {t('Inspections')} {t('as complete')}?
        </p>

        <div className="flex justify-between gap-4 mt-10">
          <Button
            type="default"
            className="w-[50%] h-[45px] text-[14px] text-[#FF9200] border border-[#FF9200] bg-[#ff910020] hover:bg-[#ff910015]"
            onClick={() => setIsModalOpen(false)}
          >
            {t('Cancel')}
          </Button>

          <Button
            type="primary"
            className="w-[50%] h-[45px] text-[14px] border-none"
            style={{
              backgroundColor: '#FF9200',
              color: 'white',
            }}
            loading={loading}
            onClick={markCompleteInspections}
          >
            {t('Complete')}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default MarkCompleteModal;
