"use client";
import React, { useEffect, useState } from "react";
import ReactJ<PERSON>ride, { CallBackProps } from "react-joyride";
import { useRouter, usePathname } from "next/navigation";

// @ts-ignore
declare global {
  interface Window {
    advanceTourToStep4?: () => void;
    isJoyrideStep3Active?: boolean;
    shouldAdvanceTourToStep4?: boolean;
    startDemoInspectionTour?: (rowKey: string) => void;
    openDemoCardForTour?: () => void;
  }
}

// Draggable tooltip image component
const DraggableTooltipImage = ({ imagePath, onDragStart }: { imagePath: string; onDragStart?: () => void }) => {
  const [isDragging, setIsDragging] = useState(false);

  const handleDragStart = (e: React.DragEvent) => {
    setIsDragging(true);
    
    // Set the drag effect
    e.dataTransfer.effectAllowed = 'copy';
    
    // For local images, we can use a simpler approach
    e.dataTransfer.setData('text/plain', imagePath);
    e.dataTransfer.setData('application/json', JSON.stringify({
      tutorialFile: true,
      fileName: 'voegwerk.jpg',
      localPath: imagePath
    }));
    
    // Also set a fallback data type
    e.dataTransfer.setData('text/html', `<img src="${imagePath}" alt="Tutorial image" />`);
    
    if (onDragStart) onDragStart();
  };

  const handleDragEnd = () => {
    setIsDragging(false);
  };

  return (
    <div style={{ textAlign: 'center' }}>
      <div
        draggable={true}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        style={{ 
          cursor: isDragging ? 'grabbing' : 'grab',
          maxWidth: '200px',
          maxHeight: '150px',
          border: `2px dashed ${isDragging ? '#28a745' : '#007bff'}`,
          padding: '8px',
          borderRadius: '8px',
          opacity: isDragging ? 0.7 : 1,
          transform: isDragging ? 'scale(0.95)' : 'scale(1)',
          transition: 'all 0.2s ease',
          userSelect: 'none',
          WebkitUserSelect: 'none',
          MozUserSelect: 'none',
          msUserSelect: 'none',
          display: 'inline-block',
          backgroundColor: isDragging ? '#f0f8ff' : '#ffffff'
        }}
      >
        <img
          src={imagePath}
          draggable={false}
          style={{ 
            maxWidth: '100%',
            maxHeight: '100%',
            pointerEvents: 'none',
            userSelect: 'none'
          }}
          alt="Tutorial image"
        />
      </div>
    </div>
  );
};

const ClientLayout = ({ children }: { children: React.ReactNode }) => {
  const router = useRouter();
  const pathname = usePathname();

  const steps = [
    {
      target: ".joyride-demo-inspection-cell",
      content: (
        <div style={{ textAlign: 'left' }}>
          <h3 style={{ margin: '0 0 8px 0', fontSize: '14px', fontWeight: 'bold' }}>Stap 1 van 6: Demo Inspectie</h3>
          <p style={{ margin: '0 0 8px 0', fontSize: '14px' }}>
            Klik op de inspectie om deze te openen en de tour te starten.
          </p>
        </div>
      ),
      placement: "bottom" as const,
    },
    {
      target: '[data-dropbox="true"]',
      content: (
        <div style={{ textAlign: 'left' }}>
          <h3 style={{ margin: '0 0 8px 0', fontSize: '14px', fontWeight: 'bold' }}>Stap 2 van 6: Maak een bevinding</h3>
          <p style={{ margin: '0 0 8px 0', fontSize: '14px' }}>
            Sleep de foto naar het oranje vlak om een bevinding aan te maken.
          </p>
          <DraggableTooltipImage 
            imagePath="/images/newFindings/voegwerk.jpg"
            onDragStart={() => {}}
          />
        </div>
      ),
      placement: "bottom" as const,
      disableOverlay: true,
    },
    {
      // Robust selector: targets any blue card with draggable ID, regardless of specific ID or text
      target: '.bg-blue-50.text-blue-600[data-rfd-draggable-id]',
      content: (
        <div style={{ textAlign: 'left' }}>
          <h3 style={{ margin: '0 0 8px 0', fontSize: '14px', fontWeight: 'bold' }}>Stap 3 van 6: Bevinding openen en goedkeuren</h3>
          <p style={{ margin: '0 0 8px 0', fontSize: '14px' }}>
            Klik op de bevinding om deze te openen.
          </p>
        </div>
      ),
      placement: "bottom" as const,
    },
    {
      // Target the center of the screen for Step 4 tooltip - better positioning
      target: "body",
      content: (
        <div style={{ textAlign: 'left' }}>
          <h3 style={{ margin: '0 0 8px 0', fontSize: '14px', fontWeight: 'bold' }}>Stap 4 van 6: Checken en goedkeuren</h3>
          <p style={{ margin: '0 0 8px 0', fontSize: '14px' }}>
            Check of alles juist is en klik op Goedkeuren.
          </p>
        </div>
      ),
      placement: "center" as const,
    },
    {
      // Step 5 - target the "Volgende" button that appears after Goedkeuren
      target: 'button[data-joyride-volgende="true"]',
      content: (
        <div style={{ textAlign: 'left' }}>
          <h3 style={{ margin: '0 0 8px 0', fontSize: '14px', fontWeight: 'bold' }}>Stap 5 van 6: Bijna klaar!</h3>
          <p style={{ margin: '0 0 8px 0', fontSize: '14px' }}>
            Klik rechtsboven op Volgende om naar het overzicht van bevindingen te gaan.
          </p>
        </div>
      ),
      placement: "center" as const,
    },
    {
      // Step 6 - findingOverview screen, center
      target: "body",
      content: (
        <div style={{ textAlign: 'left' }}>
          <h3 style={{ margin: '0 0 8px 0', fontSize: '14px', fontWeight: 'bold' }}>Stap 6 van 6: Voltooien</h3>
          <p style={{ margin: '0 0 8px 0', fontSize: '14px' }}>
            Klik op &quot;Voltooien&quot; als je de inspectie wilt exporteren. Je krijgt dan binnen een paar minuten het rapport in je mailbox.
          </p>
        </div>
      ),
      placement: "center" as const,
    },
  ];

  // Create conditional steps based on current page
  const getCurrentSteps = () => {
    
    if (pathname.includes('/newInspection')) {
      // On inspection page, show step 2 (dropbox) then step 3 (blue card) then step 4 (goedkeuren button) then step 5 (volgende button)
      if (inspectionStep === 0) {
        return [steps[1]]; // Step 2 (dropbox)
      } else if (inspectionStep === 1) {
        return [steps[2]]; // Step 3 (blue card)
      } else if (inspectionStep === 2 && !waitingForStep5) {
        return [steps[3]]; // Step 4 (goedkeuren button)
      } else if (inspectionStep === 3) {
        return [steps[4]]; // Step 5 (volgende button)
      } else if (inspectionStep === 4) {
        return [steps[5]]; // Step 6 (findingOverview)
      }
    } else if (pathname.includes('/findingOverview')) {
      if (inspectionStep === 4) {
        return [steps[5]]; // Step 6 (findingOverview)
      }
    } else if (pathname.includes('/project')) {
      // On home page (/project), only show step 1
      return [steps[0]];
    }
    
    return []; // Fallback
  };

  const [demoInspectionData, setDemoInspectionData] = useState<any>(null);
  const [stepTransitionKey, setStepTransitionKey] = useState(0);
  const [inspectionStep, setInspectionStep] = useState(0); // 0 = step 2, 1 = step 3, 2 = step 4, 3 = step 5
  const [waitingForStep5, setWaitingForStep5] = useState(false); // Prevents step 4 from showing again
  const [joyrideInstanceKey, setJoyrideInstanceKey] = useState(Date.now()); // For robust remounts
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [run, setRun] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [isNavigating, setIsNavigating] = useState(false); // Flag to prevent cleanup during navigation

  // Helper to robustly start the tour from the beginning
  const startTour = () => {
    setRun(false);
    setCurrentStepIndex(0);
    setJoyrideInstanceKey(Date.now());
    setTimeout(() => {
      setRun(true);
    }, 50);
  };

  // Prevent layout shifts when Joyride starts
  useEffect(() => {
    if (run) {
      // Prevent body scrolling and layout shifts
      document.body.style.overflow = 'hidden';
      document.body.style.position = 'fixed';
      document.body.style.width = '100%';
    } else {
      // Restore normal scrolling when Joyride ends
      document.body.style.overflow = '';
      document.body.style.position = '';
      document.body.style.width = '';
    }
  }, [run]);

  useEffect(() => {
    setIsClient(true);
    
    // Check if tour has already been completed or skipped
    if (typeof window !== 'undefined') {
      const tourCompleted = localStorage.getItem('joyride-completed');
      const tourSkipped = localStorage.getItem('joyride-skipped');
      
      // Temporarily clear for testing
      localStorage.removeItem('joyride-completed');
      localStorage.removeItem('joyride-skipped');
      
      if (tourCompleted || tourSkipped) {
        return;
      }
    }
    
    // Only start polling on the home page (/project), not on inspection page
    if (pathname.includes('/newInspection')) {
      return;
    }
    
    // Only start polling on the home page (/project)
    if (!pathname.includes('/project')) {
      return;
    }
    
    // Re-enabled polling with improved error handling
    let tries = 0;
    const interval = setInterval(() => {
      try {
        const found = document.querySelector('.joyride-demo-inspection-cell');
        
        if (found) {
          // Additional check to ensure the element is fully rendered and stable
          const rect = found.getBoundingClientRect();
          
          if (rect.width > 0 && rect.height > 0 && rect.top > 0) {
            // Try to get the demo inspection data from the table
            const tableRow = found.closest('tr');
            if (tableRow) {
              const rowKey = tableRow.getAttribute('data-row-key');
              
              // Store the row key for later use
              setDemoInspectionData({ rowKey });
            }
            
            setTimeout(() => {
              try {
                // Triple-check that the element still exists and is stable
                const elementStillExists = document.querySelector('.joyride-demo-inspection-cell');
                if (elementStillExists && !run) { // Only start if not already running
                  const finalRect = elementStillExists.getBoundingClientRect();
                  if (finalRect.width > 0 && finalRect.height > 0) {
                    setRun(true);
                    setCurrentStepIndex(0);
                  }
                }
              } catch (error) {
                // Silently handle errors
              }
            }, 2000); 
            clearInterval(interval);
          }
        } else {
          if (tries > 450) { // 45 seconds
            clearInterval(interval);
          }
        }
        tries++;
      } catch (error) {
        if (tries > 450) {
          clearInterval(interval);
        }
        tries++;
      }
    }, 100);
    
    return () => {
      clearInterval(interval);
      // Cleanup: ensure Joyride is stopped when component unmounts (unless navigating)
      if (!isNavigating) {
      setRun(false);
      setCurrentStepIndex(0);
      setDemoInspectionData(null);
      }
    };
  }, [pathname]);

  // Cleanup effect to ensure Joyride is stopped when navigating away
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (!isNavigating) {
      setRun(false);
      setCurrentStepIndex(0);
      setDemoInspectionData(null);
      }
    };

    const handleVisibilityChange = () => {
      if (document.hidden && !isNavigating) {
        setRun(false);
        setCurrentStepIndex(0);
        setDemoInspectionData(null);
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      // Final cleanup (unless navigating)
      if (!isNavigating) {
      setRun(false);
      setCurrentStepIndex(0);
      setDemoInspectionData(null);
      }
    };
  }, [isNavigating]);

  // Polling for second step element on inspection page
  useEffect(() => {
    // Check if we should restore tour state on inspection page
    if (pathname.includes('/newInspection') && isClient) {
      const shouldContinueTour = localStorage.getItem('joyride-continue-on-inspection');
      if (shouldContinueTour === 'true') {
        localStorage.removeItem('joyride-continue-on-inspection');
        
        // Get the inspection step from localStorage
        const savedInspectionStep = localStorage.getItem('joyride-inspection-step');
        const stepToContinue = savedInspectionStep ? parseInt(savedInspectionStep) : 0;
        localStorage.removeItem('joyride-inspection-step');
        
        setInspectionStep(stepToContinue);
        
        // Add a small delay to ensure the page is fully loaded
        setTimeout(() => {
          setRun(true);
          setCurrentStepIndex(0);
        }, 1000);
      }
    }

    // Only start polling if we're on the inspection page and tour is running and tour was properly initiated
    if (!run || !isClient || !pathname.includes('/newInspection') || inspectionStep !== 0) {
      return;
    }
    
    // Check if tour was properly initiated (either from step 1 or restored from localStorage)
    const tourContinueFlag = typeof window !== 'undefined' ? localStorage.getItem('joyride-continue-on-inspection') : null;
    if (!demoInspectionData && tourContinueFlag !== 'true') {
      return;
    }

    let tries = 0;
    const maxTries = 300;
    const interval = setInterval(() => {
      try {
        const targetElement = document.querySelector('[data-dropbox="true"]');
        
        if (targetElement) {
          // Additional check to ensure the element is fully rendered and stable
          const rect = targetElement.getBoundingClientRect();
          
          if (rect.width > 0 && rect.height > 0 && rect.top > 0) {
            setTimeout(() => {
              try {
                // Triple-check that the element still exists and is stable
                const elementStillExists = document.querySelector('[data-dropbox="true"]');
                if (elementStillExists && !run) { // Only start if not already running
                  const finalRect = elementStillExists.getBoundingClientRect();
                  if (finalRect.width > 0 && finalRect.height > 0) {
                    setRun(true);
                    setCurrentStepIndex(0);
                  }
                }
              } catch (error) {
                // Silently handle errors
              }
            }, 500);
            
            clearInterval(interval);
            return;
          }
        } else {
          if (tries >= maxTries) {
            clearInterval(interval);
            setRun(false);
            setCurrentStepIndex(0);
          }
        }
        tries++;
      } catch (error) {
        if (tries >= maxTries) {
          clearInterval(interval);
        }
        tries++;
      }
    }, 100);

    return () => {
      clearInterval(interval);
    };
  }, [run, isClient, pathname]);

  const handleJoyrideCallback = (data: any) => {
    const { action, index, type } = data;

    // Handle step completion - navigate to inspection page if on home page
    if (type === 'step:after' && action === 'next' && index === 0 && !pathname.includes('/newInspection') && demoInspectionData?.rowKey) {
      if (window.startDemoInspectionTour) {
        window.startDemoInspectionTour(demoInspectionData.rowKey);
      }
      return; // Exit early to prevent other handlers from running
    }

    // Handle Step 2 (dropbox) - if user clicks "Volgende" without dragging, reshow the tooltip
    if (type === 'step:after' && action === 'next' && pathname.includes('/newInspection') && inspectionStep === 0) {
      setRun(false);
      setTimeout(() => {
        setRun(true);
        setCurrentStepIndex(0);
      }, 100);
      return; // Exit early to prevent other handlers from running
    }

    // Handle Step 3 completion - open the card programmatically if advancing via Joyride
    if (type === 'step:after' && action === 'next' && pathname.includes('/newInspection') && inspectionStep === 1) {
      if (window.openDemoCardForTour) {
        window.openDemoCardForTour();
      }
      // The rest of the logic (advance to step 4)
      setRun(false);
      setInspectionStep(2);
      setTimeout(() => setRun(true), 200);
      return;
    }

    // Handle Step 4 completion - transition to Step 5
    if (type === 'step:after' && action === 'next' && pathname.includes('/newInspection') && inspectionStep === 2) {
      setWaitingForStep5(true); // Prevent step 4 from showing again
      setRun(false); // Hide Joyride
      // Poll for the disappearance of the Goedkeuren button
      let tries = 0;
      const maxTries = 300; // 30s at 100ms interval
      const pollForGoedkeurenGone = () => {
        const goedkeurenButtons = Array.from(document.querySelectorAll('button.ant-btn.ant-btn-primary'));
        const goedkeurenStillPresent = goedkeurenButtons.some(btn => (btn.textContent || '').includes('Goedkeuren') && (btn as HTMLElement).offsetParent !== null);
        if (!goedkeurenStillPresent) {
          // Goedkeuren button is gone, now poll for Volgende button
          pollForVolgendeButton();
          return;
        }
        if (tries < maxTries) {
          tries++;
          setTimeout(pollForGoedkeurenGone, 100);
        } else {
          // Timeout, end the tour
          setRun(false);
          setCurrentStepIndex(0);
          setInspectionStep(0);
          setWaitingForStep5(false);
          if (typeof window !== 'undefined') {
            localStorage.setItem('joyride-completed', 'true');
          }
        }
      };
      // Poll for the Volgende button after Goedkeuren is gone
      const pollForVolgendeButton = () => {
        let tries = 0;
        const maxTries = 300; // 30s at 100ms interval
        const poll = () => {
          const buttons = document.querySelectorAll('button.ant-btn.ant-btn-primary');
          let volgendeButton = null;
          for (const button of Array.from(buttons)) {
            const buttonText = button.textContent || '';
            if (buttonText.includes('Volgende')) {
              volgendeButton = button;
              break;
            }
          }
          if (volgendeButton && (volgendeButton as HTMLElement).offsetParent !== null) {
            // Button is present and visible
            (volgendeButton as HTMLElement).setAttribute('data-joyride-volgende', 'true');
            setInspectionStep(3); // Move to step 5
            setWaitingForStep5(false);
            setRun(false); // Stop current tour
            setTimeout(() => {
              setRun(true); // Start new tour with Step 5
              setCurrentStepIndex(0);
            }, 100);
            return;
          }
          if (tries < maxTries) {
            tries++;
            setTimeout(poll, 100);
          } else {
            // Timeout, end the tour
            setRun(false);
            setCurrentStepIndex(0);
            setInspectionStep(0);
            setWaitingForStep5(false);
            if (typeof window !== 'undefined') {
              localStorage.setItem('joyride-completed', 'true');
            }
          }
        };
        poll();
      };
      pollForGoedkeurenGone();
      return; // Exit early to prevent other handlers from running
    }

    // After Step 5, navigate to findingOverview and set flag for Step 6
    if (type === 'step:after' && action === 'next' && inspectionStep === 3) {
      if (typeof window !== 'undefined') {
        localStorage.setItem('joyride-show-step-6', 'true');
      }
      setRun(false);
      setCurrentStepIndex(0);
      setIsNavigating(true);
      
      // Navigate to findingOverview page
      router.push('/newInspection/findingOverview');
      
      // Poll for navigation completion
      let tries = 0;
      const maxTries = 300; // 30s
      const pollForFindingOverview = () => {
        const currentPath = typeof window !== 'undefined' ? window.location.pathname : pathname;
        if (currentPath && currentPath.includes('/findingOverview')) {
          setInspectionStep(4); // Move to step 6
          setTimeout(() => {
            setRun(true);
            setCurrentStepIndex(0);
            setIsNavigating(false);
          }, 100);
          return;
        }
        if (tries < maxTries) {
          tries++;
          setTimeout(pollForFindingOverview, 100);
        } else {
          setInspectionStep(0);
          setIsNavigating(false);
        }
      };
      pollForFindingOverview();
      return;
    }

    if (type === 'step:after' && action === 'prev') {
      setCurrentStepIndex(index - 1);
    }

    if (type === 'step:after' && action === 'close') {
      // Handle step 2 close - restart the step 2 tooltip
      if (pathname.includes('/newInspection') && inspectionStep === 0) {
        setRun(false);
        setTimeout(() => {
          setRun(true);
          setCurrentStepIndex(0);
        }, 1000);
        return; // Exit early to prevent normal close behavior
      }
      
      setCurrentStepIndex(0);
      setRun(false);
      // Clear any stored data
      setDemoInspectionData(null);
    }

    if (type === 'tour:end') {
      setCurrentStepIndex(0);
      setRun(false);
      // Clear any stored data
      setDemoInspectionData(null);
      // Store in localStorage that the tour has been completed
      if (typeof window !== 'undefined') {
        localStorage.setItem('joyride-completed', 'true');
      }
    }

    if (type === 'tour:start') {
      setCurrentStepIndex(0);
    }

    if (type === 'step:after' && action === 'skip') {
      setCurrentStepIndex(0);
      setRun(false);
      // Clear any stored data
      setDemoInspectionData(null);
      // Store in localStorage that the tour has been skipped
      if (typeof window !== 'undefined') {
        localStorage.setItem('joyride-skipped', 'true');
      }
    }

    // Handle overlay click to advance to next step
    if (type === 'overlay:click') {
      setCurrentStepIndex(index + 1);
    }

    if (type === 'step:after' && action === 'next') {
      setCurrentStepIndex(index + 1);
    }
  };

  useEffect(() => {
    window.startDemoInspectionTour = (rowKey) => {
      if (typeof window !== 'undefined') {
        localStorage.setItem('joyride-continue-on-inspection', 'true');
        localStorage.setItem('joyride-inspection-step', '0');
      }
      if (rowKey) {
        router.push(`/newInspection?inspectionId=${rowKey}`);
      }
    };
    return () => {
      delete window.startDemoInspectionTour;
    };
  }, [router]);

  // On mount, if on /findingOverview and flag is set, start Step 6
  useEffect(() => {
    // Reset navigating flag when component mounts on new page
    if (isNavigating) {
      setIsNavigating(false);
    }
    
    if (pathname && pathname.includes('/findingOverview')) {
      if (typeof window !== 'undefined' && localStorage.getItem('joyride-show-step-6') === 'true') {
        localStorage.removeItem('joyride-show-step-6');
        setInspectionStep(4);
        setRun(false);
        setCurrentStepIndex(0);
        setJoyrideInstanceKey(Date.now());
        setTimeout(() => {
          setRun(true);
        }, 2000);
      }
    }
  }, [pathname, isNavigating]);

  useEffect(() => {
    window.shouldAdvanceTourToStep4 = inspectionStep === 1 && pathname.includes('/newInspection');
    window.advanceTourToStep4 = () => {
      setInspectionStep(2);
      setRun(false);
      setCurrentStepIndex(0);
      setTimeout(() => setRun(true), 1000);
    };
    return () => {
      delete window.advanceTourToStep4;
      delete window.shouldAdvanceTourToStep4;
    };
  }, [inspectionStep, pathname]);

  useEffect(() => {
    function handleCloseStep2() {
      setRun(false);
      setInspectionStep(1); // Move to step 3 (blue card)
    }
    window.addEventListener('close-joyride-step-2', handleCloseStep2);
    return () => window.removeEventListener('close-joyride-step-2', handleCloseStep2);
  }, []);

  useEffect(() => {
    if (!isClient || !pathname.includes('/newInspection') || inspectionStep !== 1) {
      return;
    }

    let tries = 0;
    const maxTries = 300; // 30 seconds
    const interval = setInterval(() => {
      try {
        // Only match the real card, not the loading placeholder
        const targetElement = document.querySelector('.bg-blue-50.text-blue-600.animate-pulse.border.shadow-sm');
        if (targetElement) {
          const rect = targetElement.getBoundingClientRect();
          if (rect.width > 0 && rect.height > 0 && rect.top > 0) {
            setTimeout(() => {
              const elementStillExists = document.querySelector('.bg-blue-50.text-blue-600.animate-pulse.border.shadow-sm');
              if (elementStillExists && !run) {
                setRun(true);
                setCurrentStepIndex(0);
              }
            }, 500);
            clearInterval(interval);
            return;
          }
        }
        if (tries >= maxTries) {
          clearInterval(interval);
        }
        tries++;
      } catch (error) {
        if (tries >= maxTries) {
          clearInterval(interval);
        }
        tries++;
      }
    }, 100);

    return () => {
      clearInterval(interval);
    };
  }, [isClient, pathname, inspectionStep]);

  // Monitor for Goedkeuren button click when step 4 is active
  useEffect(() => {
    if (!run || !isClient || !pathname.includes('/newInspection') || inspectionStep !== 2 || waitingForStep5) {
      return;
    }
    
    // Poll for the disappearance of the Goedkeuren button
    let tries = 0;
    const maxTries = 300; // 30s at 100ms interval
    const pollForGoedkeurenGone = () => {
      const goedkeurenButtons = Array.from(document.querySelectorAll('button.ant-btn.ant-btn-primary'));
      const goedkeurenStillPresent = goedkeurenButtons.some(btn => (btn.textContent || '').includes('Goedkeuren') && (btn as HTMLElement).offsetParent !== null);
      if (!goedkeurenStillPresent) {
        // Goedkeuren button is gone, now poll for Volgende button
        setWaitingForStep5(true); // Prevent step 4 from showing again
        setRun(false); // Hide current step
        pollForVolgendeButton();
        return;
      }
      if (tries < maxTries) {
        tries++;
        setTimeout(pollForGoedkeurenGone, 100);
      } else {
        // Timeout, end the tour
        setRun(false);
        setCurrentStepIndex(0);
        setInspectionStep(0);
        setWaitingForStep5(false);
        if (typeof window !== 'undefined') {
          localStorage.setItem('joyride-completed', 'true');
        }
      }
    };

    // Poll for the Volgende button after Goedkeuren is gone
    const pollForVolgendeButton = () => {
      let tries = 0;
      const maxTries = 300; // 30s at 100ms interval
      const poll = () => {
        const buttons = document.querySelectorAll('button.ant-btn.ant-btn-primary');
        let volgendeButton = null;
        for (const button of Array.from(buttons)) {
          const buttonText = button.textContent || '';
          if (buttonText.includes('Volgende')) {
            volgendeButton = button;
            break;
          }
        }
        if (volgendeButton && (volgendeButton as HTMLElement).offsetParent !== null) {
          // Button is present and visible
          (volgendeButton as HTMLElement).setAttribute('data-joyride-volgende', 'true');
          setInspectionStep(3); // Move to step 5
          setWaitingForStep5(false);
          setRun(false); // Stop current tour
          setTimeout(() => {
            setRun(true); // Start new tour with Step 5
            setCurrentStepIndex(0);
          }, 2000);
          return;
        }
        if (tries < maxTries) {
          tries++;
          setTimeout(poll, 100);
        } else {
          // Timeout, end the tour
          setRun(false);
          setCurrentStepIndex(0);
          setInspectionStep(0);
          setWaitingForStep5(false);
          if (typeof window !== 'undefined') {
            localStorage.setItem('joyride-completed', 'true');
          }
        }
      };
      poll();
    };

    // Start polling immediately when step 4 appears
    setTimeout(pollForGoedkeurenGone, 2000); // Small delay to let step 4 settle

  }, [run, isClient, pathname, inspectionStep, waitingForStep5]);

  return (
    <>
      {isClient && run && getCurrentSteps().length > 0 && (
        <ReactJoyride
          key={`joyride-${joyrideInstanceKey}`}
          steps={getCurrentSteps()}
          run={run}
          stepIndex={currentStepIndex}
          callback={handleJoyrideCallback}
          continuous={inspectionStep !== 0 && inspectionStep !== 1 && inspectionStep !== 2 && inspectionStep !== 3 && inspectionStep !== 4}
          showSkipButton
          scrollToFirstStep={false}
          disableScrolling={true}
          spotlightPadding={0}
          disableOverlayClose={false}
          disableOverlay={false}
          showProgress={true}
          scrollOffset={100}
          locale={{
            last: "Volgende",
            skip: "Overslaan",
            close: "Sluiten",
            next: "Volgende",
            back: "Terug"
          }}
          styles={{
            options: {
              zIndex: 10000,
              arrowColor: "#fff",
              backgroundColor: "#fff",
              overlayColor: "rgba(0, 0, 0, 0.4)",
              primaryColor: "#0070f3",
              textColor: "#333",
            },
            buttonNext: {
              fontSize: "14px",
            },
            buttonBack: {
              fontSize: "14px",
            },
            buttonSkip: {
              fontSize: "14px",
            },
            buttonClose: {
              fontSize: "14px",
            },
            tooltip: {
              position: "fixed",
              transform: "none",
              left: "auto",
              right: "auto",
              top: "auto",
              bottom: "auto",
              width: "auto",
              maxWidth: "800px",
              minWidth: "450px",
              fontFamily: "'Open Sans', sans-serif",
              pointerEvents: "auto",
              zIndex: 10001,
              ...(inspectionStep === 3 ? { marginTop: '15px' } : {}),
            },
            overlay: {
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              pointerEvents: "none", // Allow events to pass through to underlying elements
              zIndex: 9999,
            },
            spotlight: {
              pointerEvents: "none", // Allow events to pass through the spotlight area
              zIndex: 9998,
            },
          }}
        />
      )}
      {children}
    </>
  );
};

export default ClientLayout;
