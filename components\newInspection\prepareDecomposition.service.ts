import { fetchParentAPIValue } from "@/src/services/newFindings.api";
import { addFindingDetailsWithMedia, fetchTextSuggestions, generateFindings, updateFindingOrderForNewDecomp, updateFinfingOrder } from "@/src/services/newInspection.api";
import { message } from "antd";
import { createfieldvaluesObject } from "./utils";
import { Timestamp } from "firebase/firestore";
import { mapFiledsWithValue } from "@/src/services/fieldsMapping";

export const prepareDecomposition = async (
    files: File[],
    type: "decomposition" | "finding",
    setNumberOfProcessingDecomp: any,
    setOrderArray: any,
    orderArray: any,
    decompositionGroups: any,
    setLoader: any,
    t: any,
    fetchFindings: any
  ) => {
    try {
      // Filter for only images and videos
      const mediaFiles = files.filter(
        (file) =>
          file.type.startsWith("image/") || file.type.startsWith("video/")
      );

      if (mediaFiles.length === 0) {
        message.error(t("Please select image or video files"));
        return;
      }

      let imagesArray: any = [];
      let videosArray: any = [];

      mediaFiles.forEach((file: any) => {
        const fileType = file.type.split("/")[0];
        if (fileType === "image") {
          imagesArray.push(file);
        } else if (fileType === "video") {
          videosArray.push(file);
        }
      });

      if (imagesArray.length === 0) {
        message.error(t("At least one image is required"));
        return;
      }

      const findingType = sessionStorage.getItem("FindingTypeId");
      const inspectionType = localStorage.getItem("reportTypeId");
      const inspection_id = localStorage.getItem("ScalarInspectionId");

      const res: any = await generateFindings(
        imagesArray,
        videosArray,
        setLoader,
        inspection_id,
        "decomp",
        inspectionType,
        "null"
      );

      if (res) {
        const parentFindingId: any =
          sessionStorage.getItem("parent_finding_id");
        let payload: any;
        if (res?.resize_media) {
          const image_urls = res?.resize_media.map((media: any) => media.url);
          payload = {
            image_urls: image_urls,
            parent_finding: parentFindingId,
            suggestions: res.suggestions,
          };
        } else {
          payload = {
            image_urls: [],
            parent_finding: parentFindingId,
            suggestions: res.suggestions,
          };
        }

        let fieldsAfterUpdate: any = res?.suggestions?.fields;

        const allTextFields = fieldsAfterUpdate
          .filter((field: any) => field.type === "text")

        const allTextFieldsNull = allTextFields
          .every((field: any) => field.suggested_value === null);

        if (allTextFields.length > 0 && allTextFieldsNull) {
          const textSuggestionRes = await fetchTextSuggestions(
            inspectionType,
            inspection_id,
            "decomp",
            payload
          );

          if (textSuggestionRes) {
            const textNumberFields = res?.suggestions?.fields.filter(
              (field: any) => field.type === "text" || field.type === "number"
            );
            // const mappedTextFields = mapFiledsWithValue(fieldsAfterUpdate, textSuggestionRes.suggestions.fields)
            fieldsAfterUpdate = textSuggestionRes.suggestions.fields.map(
              (field: any) => {
                const textNumField = textNumberFields.find(
                  (f: any) => f.id === field.id
                );
                if (textNumField) {
                  return {
                    ...field,
                    suggested_value: textNumField.suggested_value,
                  };
                } else {
                  return field;
                }
              }
            );
          }
        }

        const parentAPIField = fieldsAfterUpdate.find(
          (field: any) => field.type === "parent_api"
        );
        if (parentAPIField) {
          const payload = {
            parent_finding_id: "noid",
          };
          const parentRes = await fetchParentAPIValue(
            inspectionType,
            inspection_id,
            "decomp",
            parentAPIField?.id,
            payload
          );

          if (parentRes) {
            // const value = parseInt(parentRes?.result?.value);

            const updatedFields = fieldsAfterUpdate.map((field: any) => {
              if (field.type === "parent_api") {
                return {
                  ...field,
                  suggested_value: parentRes.result.value,
                };
              } else {
                return field;
              }
            });
            fieldsAfterUpdate = updatedFields;
          }
        }

        const clientId = localStorage.getItem("client");
        const final_fields = fieldsAfterUpdate.map((field: any) => {
          if (field.id === "image_field") {
            return {
              ...field,
              suggested_value: null,
            };
          } else return field;
        });
        const field_values = await createfieldvaluesObject(final_fields);
        const timeStamp = Timestamp.now();

        const updatedFields = final_fields.map((field: any) => {
          if (field.suggested_value === null) {
            return {
              ...field,
              suggested_value: "",
            };
          } else return field;
        });

        const findingDetails: any = {
          fields: JSON.stringify(updatedFields),
          title_fields: res?.suggestions?.title_fields,
          media: [],
          client: clientId,
          ...(field_values && { field_values: field_values }),
          parent_finding_id: null,
          ...(timeStamp && { created_at: timeStamp }),
          ...(timeStamp && { updated_at: timeStamp }),
          source: "web",
          approved: false,
        };
        const addFindingRes: any = await addFindingDetailsWithMedia(
          inspection_id,
          findingDetails,
          "decomp",
          res?.resize_media,
          imagesArray
        );

        if (addFindingRes && addFindingRes.id) {
          // setApprovalId(addFindingRes.id)

          // Start background process to upload media
          // const updatedDoc = await uploadMediaInBackground(
          //   addFindingRes.id,
          //   res?.resize_media,
          //   imagesArray
          // );

          // Add the new decomposition directly to decompositionGroups
          if (type === "decomposition") {
            // Also update orderArray to include the new decomposition
            setOrderArray((prev: any) => [
              ...prev,
              {
                id: addFindingRes.id,
                type: "decomposition",
                parentId: null,
              },
            ]);
          }
        }

        // Update the order in localStorage
        // const newOrderIds = [
        //   ...orderArray,
        //   {
        //     id: addFindingRes.id,
        //     type: "decomposition",
        //     parentId: null,
        //   },
        // ].map((item) => item.id);
        updateFindingOrderForNewDecomp(addFindingRes.id);

        // setRefresh((prev: any) => !prev);
        await fetchFindings();
      }
    } catch (error) {
      console.error("Error preparing decomposition:", error);
      message.error(t("Something went wrong, try again later!"));
    } finally {
      setNumberOfProcessingDecomp((prev: any) => prev - 1);
    }
  };