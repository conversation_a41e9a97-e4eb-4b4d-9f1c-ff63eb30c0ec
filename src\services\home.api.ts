import { db } from "@/firebase.config";
import {
  collection,
  getDocs,
  query,
  where,
  doc,
  deleteDoc,
  updateDoc,
  arrayRemove,
  getDoc,
  orderBy,
  limit,
  startAfter,
  or,
} from "firebase/firestore";

export const fetchAllDocuments = async (userId: any, projectId: any) => {
  try {
    const UserRef = doc(db, "user", userId);
    const projectRef = doc(db, "projects", projectId);
    const collectionRef = collection(db, "inspection");
    const clientId = localStorage.getItem("client");

    const condition = query(
      collectionRef,
      where("projectList", "array-contains", projectRef),
      where("client", "==", clientId)
    );

    const querySnapshot = await getDocs(condition);
    console.log("QuerySnapshot size:", querySnapshot.size);

    const documents = querySnapshot.docs.map((doc: any) => ({
      id: doc.id,
      ...doc.data(),
    }));
    await documents.sort((a, b) => b.created_at.seconds - a.created_at.seconds);
    return documents;
  } catch (error) {
    return false;
  }
};

export const getInspections = async ({
  search = "",
  projectId = "",
  isForFirst = true,
  lastDocumentName = null,
  lastDocumentQRCode = null,
  limitValue = 10,
}: any) => {
  const clientId = localStorage.getItem("client");
  if (!clientId) throw new Error("Client ID not found");

  let inspectionList: any = [];
  let hasMoreName = false;
  let hasMoreQRCode = false;

  const projectRef = doc(db, "projects", projectId);

  if (search) {
    // Common filters
    const commonFilters = [
      where("projectList", "array-contains", projectRef),
      where("client", "==", clientId),
    ];

    // Name query
    const nameQuery = query(
      collection(db, "inspection"),
      ...commonFilters,
      where("search_name", ">=", search),
      where("search_name", "<=", `${search}\uf8ff`),
      orderBy("name"),
      orderBy("created_at", "desc"),
      ...(isForFirst || !lastDocumentName ? [] : [startAfter(lastDocumentName)]),
      limit(limitValue)
    );

    // qr_code query
    const qrQuery = query(
      collection(db, "inspection"),
      ...commonFilters,
      where("qr_code", ">=", search),
      where("qr_code", "<=", `${search}\uf8ff`),
      orderBy("qr_code"),
      orderBy("created_at", "desc"),
      ...(isForFirst || !lastDocumentQRCode ? [] : [startAfter(lastDocumentQRCode)]),
      limit(limitValue)
    );

    // Fetch both queries
    const [nameSnap, qrSnap] = await Promise.all([getDocs(nameQuery), getDocs(qrQuery)]);

    // Deduplicate and merge results
    const docsMap = new Map();
    nameSnap.docs.forEach((doc) => docsMap.set(doc.id, doc));
    qrSnap.docs.forEach((doc) => docsMap.set(doc.id, doc));

    const mergedDocs = Array.from(docsMap.values());

    // Map documents to inspection list
    inspectionList = mergedDocs.map((doc) => ({
      ...doc.data(),
      id: doc.id,
    }));

    hasMoreName = nameSnap.docs.length === limitValue;
    hasMoreQRCode = qrSnap.docs.length === limitValue;

    return {
      list: inspectionList,
      hasMore: hasMoreName || hasMoreQRCode,
      lastDocumentName: nameSnap.docs.length > 0 ? nameSnap.docs[nameSnap.docs.length - 1] : null,
      lastDocumentQRCode: qrSnap.docs.length > 0 ? qrSnap.docs[qrSnap.docs.length - 1] : null,
    };
  } else {
    // Standard query without search
    let queryRef = query(
      collection(db, "inspection"),
      where("projectList", "array-contains", projectRef),
      where("client", "==", clientId),
      orderBy("created_at", "desc"),
      ...(isForFirst || !lastDocumentName ? [] : [startAfter(lastDocumentName)]),
      limit(limitValue)
    );

    const snapshot = await getDocs(queryRef);

    inspectionList = snapshot.docs.map((doc) => ({
      ...doc.data(),
      id: doc.id,
    }));

    hasMoreName = snapshot.docs.length === limitValue;

    return {
      list: inspectionList,
      hasMore: hasMoreName,
      lastDocumentName: snapshot.docs.length > 0 ? snapshot.docs[snapshot.docs.length - 1] : null,
      lastDocumentQRCode, // qr_code pagination state remains unchanged
    };
  }
};


export const fetchAllInspections = async (userId: any, projectIds: any[]) => {
  try {
    const collectionRef = collection(db, "inspection");
    const UserRef = doc(db, "user", userId);
    const clientId = localStorage.getItem("client");

    if (!clientId) {
      throw new Error("Client ID not found in localStorage");
    }

    // Create an array of promises to fetch documents for each project
    const promises = projectIds.map((projectId) => {
      const projectRef = doc(db, "projects", projectId);
      const condition = query(
        collectionRef,
        where("project", "==", projectRef),
        where("client", "==", clientId)
      );

      return getDocs(condition).then((querySnapshot) => {
        return querySnapshot.docs.map((doc: any) => ({
          id: doc.id,
          ...doc.data(),
        }));
      });
    });

    // Fetch all data in parallel
    const results = await Promise.all(promises);

    // Flatten the results into a single array
    let allDocuments = results.flat();

    // Sort all documents by the created_at field in descending order
    allDocuments.sort((a, b) => b.created_at.seconds - a.created_at.seconds);

    return allDocuments;
  } catch (error) {
    console.error("Error fetching inspections:", error);
    return false;
  }
};

export const fetchInspectionsByIds = async (ids: string[]) => {
  try {
    if (!ids || ids.length === 0) {
      throw new Error("No IDs provided");
    }

    // Wrap each fetch in its own try/catch so errors for individual IDs are skipped
    const fetchPromises = ids.map(async (id) => {
      try {
        const docRef = doc(db, "inspection", id);
        const docSnap = await getDoc(docRef);
        if (!docSnap.exists()) {
          // Document does not exist, skip it
          return null;
        }
        return { id: docSnap.id, ...docSnap.data() };
      } catch (innerErr) {
        // console.error(`Error fetching inspection with ID ${id}:`, innerErr);
        // Skip this ID if it fails
        return null;
      }
    });

    // Await all fetches in parallel
    const results = await Promise.all(fetchPromises);
    // Filter out any null entries (failed or non-existent docs)
    const documents = results.filter((item) => item !== null) as any[];

    return documents;
  } catch (error) {
    console.error("Error fetching inspections:", error);
    return false;
  }
};

export const deleteInspection = async (
  inspectionDetails: any, // Optional parameter if additional details are needed
  documentId: string,
  projectId: string
) => {
  try {
    // Reference to the test_project document
    const projectDocRef = doc(db, "projects", projectId);

    // Check if the project document exists
    const projectSnapshot = await getDoc(projectDocRef);
    if (!projectSnapshot.exists()) {
      throw new Error("Project not found");
    }

    // Update the inspections array by removing the document reference
    const inspectionReference = doc(db, "inspection", documentId);
    await updateDoc(projectDocRef, {
      inspections: arrayRemove(inspectionReference),
    });

    // Delete the inspection document from the inspection collection
    const inspectionDocRef = doc(db, "inspection", documentId);
    await deleteDoc(inspectionDocRef);

    return true;
  } catch (error) {
    return false;
  }
};
