"use client";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import Image from "next/image";
import { Input, Button, message, Spin } from "antd";
import { Open_Sans } from "next/font/google";
import { GoogleOutlined } from "@ant-design/icons";
import MicrosoftIcon from "@mui/icons-material/Microsoft";
import AppleIcon from "@mui/icons-material/Apple";
import { EyeOutlined, EyeInvisibleOutlined } from "@ant-design/icons";
import {
  signInWithFirebase,
  loginWithGoogle,
  loginWithMicrosoft,
  loginWithApple,
  getUserReportTypes,
  forgotPasswordWithFirebase,
} from "@/src/services/auth.api";
import SignInLoader from "@/components/signInLoader/signLoader";
import { useTranslation } from "react-i18next";
import { handleChangeLanguage } from "@/context/LanguageContext";
import { signInWithProvider } from "@/src/services/signInProvider.api";

const OpenSans = Open_Sans({ 
  weight: ["600", "700"], 
  subsets: ["latin"] 
});

const Login = () => {
  const { t } = useTranslation();
  const [googleHovered, setGoogleHovered] = useState(false);
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [microsoftHovered, setMicrosoftHovered] = useState(false);
  const [appleHovered, setAppleHovered] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [emailError, setEmailError] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [isForgotPassword, setIsForgotPassword] = useState(false);
  const [emailFocus, setEmailFocus] = useState(false);
  const [passwordFocus, setPasswordFocus] = useState(false);

  const router = useRouter();

  useEffect(() => {
    handleChangeLanguage("nl");
  }, []);

  const handleGoogleMouseEnter = () => {
    setGoogleHovered(true);
  };

  const handleGoogleMouseLeave = () => {
    setGoogleHovered(false);
  };

  const handleMicrosoftMouseEnter = () => {
    setMicrosoftHovered(true);
  };

  const handleMicrosoftMouseLeave = () => {
    setMicrosoftHovered(false);
  };

  const handleAppleMouseEnter = () => {
    setAppleHovered(true);
  };

  const handleAppleMouseLeave = () => {
    setAppleHovered(false);
  };

  // const togglePasswordVisibility = () => {
  //     setPasswordVisible(!passwordVisible);
  // };

  const validateEmail = (email: string) => {
    const lowercaseEmail = email.toLowerCase();

    if (email !== lowercaseEmail) {
      return { type: false, message: t("Email must be in lowercase") };
    }

    const re = /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/;
    if (!re.test(email)) {
      return { type: false, message: t("Invalid email format") };
    }

    return { type: true, message: t("Valid email") };
  };

  const handleLogin = async () => {
    let emailValid = true;
    let passwordValid = true;
    if (!email) {
      setEmailError(t("Email required"));
      emailValid = false;
    } else if (!validateEmail(email).type) {
      setEmailError(validateEmail(email).message);
      emailValid = false;
    } else {
      setEmailError("");
    }

    if (!password) {
      setPasswordError(t("Password required"));
      setError("");
      passwordValid = false;
    } else {
      setPasswordError("");
      setError("");
    }

    if (passwordValid && emailValid) {
      try {
        setLoading(true);
        const { userCredential, idToken } = await signInWithFirebase(
          email,
          password
        );
        if (idToken) {
          // await getUserReportTypes(userCredential?.user.uid);
          localStorage.setItem("isJoyrideDisplayed", "false")
          const { isTermsAccepted, userId }: any = await getUserReportTypes(
            userCredential?.user.uid
          );
          if (isTermsAccepted) {
            localStorage.setItem("showTermsModal", "false");
          } else {
            localStorage.setItem("showTermsModal", "true");
          }
          localStorage.setItem("ScalarIdToken", idToken);
          localStorage.setItem("ScalarUserId", userId);
          localStorage.setItem("ScalarFirebaseUid", userCredential.user.uid);
          localStorage.setItem("ScalarUserEmail", userCredential.user.email);
          localStorage.setItem("ScalarLanguage", "nl");
          localStorage.setItem(
            "ScalarUserName",
            userCredential.user.displayName
          );
          router.push("/home");
          message.success(t("Login successful"));
        }
      } catch (error: unknown) {
        if (error instanceof Error) {
          const errorMessage = error.message
            ? "Incorrect Email or Password!"
            : "";
          setError(errorMessage);
          message.error(t("Incorrect Email or Password!"));
        } else {
          message.error(t("An unknown error occurred."));
        }
      } finally {
        setLoading(false);
      }
    }
  };

  const handleSignInWithGoogle = async () => {
    try {
      const user: any = await loginWithGoogle();
      // const res = await signInWithProvider("google");
      if (user) {
        // const user = res?.user;
        const { userId, isTermsAccepted }: any = await getUserReportTypes(
          user.uid
        );
        localStorage.setItem("ScalarIdToken", user.accessToken);
        localStorage.setItem("ScalarUserId", userId);
        localStorage.setItem("ScalarFirebaseUid", user.uid);
        localStorage.setItem("ScalarUserEmail", user.email);
        localStorage.setItem("ScalarUserName", user.displayName);
        localStorage.setItem("ScalarLanguage", "nl");
        localStorage.setItem("isJoyrideDisplayed", "false")
        if (isTermsAccepted) {
          localStorage.setItem("showTermsModal", "false");
        } else {
          localStorage.setItem("showTermsModal", "true");
        }
        message.success(t("Login successful"));
        router.push("/home");
      }
    } catch (error: any) {
      if (error.message === "Missing or insufficient permissions.") {
        message.error(t("No account found with this email address."));
      }
      if (error.message === "User not found.") {
        message.error(t("No account found with this email address."));
      }
      if (error.message === "No allowed report types found for the user.") {
        message.error(t("No allowed report types found for the user."));
      }
    }
  };

  const handleSignInWithMicrosoft = async () => {
    try {
      const user: any = await loginWithMicrosoft();
      // const res = await signInWithProvider("microsoft");
      if (user) {
        // const user = res?.user;
        const { userId, isTermsAccepted }: any = await getUserReportTypes(
          user.uid
        );
        localStorage.setItem("ScalarIdToken", user.accessToken);
        localStorage.setItem("ScalarUserId", userId);
        localStorage.setItem("ScalarFirebaseUid", user.uid);
        localStorage.setItem("ScalarUserEmail", user.email);
        localStorage.setItem("ScalarUserName", user.displayName);
        localStorage.setItem("ScalarLanguage", "nl");
        localStorage.setItem("isJoyrideDisplayed", "false")
        if (isTermsAccepted) {
          localStorage.setItem("showTermsModal", "false");
        } else {
          localStorage.setItem("showTermsModal", "true");
        }
        message.success(t("Login successful"));
        router.push("/home");
      }
    } catch (error: any) {
      if (error.message === "Missing or insufficient permissions.") {
        message.error(t("No account found with this email address."));
      }
      if (error.message === "User not found.") {
        message.error(t("No account found with this email address."));
      }
      if (error.message === "No allowed report types found for the user.") {
        message.error(t("No allowed report types found for the user."));
      }
    }
  };

  const handleSignInWithApple = async () => {
    try {
      const res: any = await loginWithApple();
      // const res: any = await signInWithProvider("apple");
      if (res?.user) {
        const user = res?.user;
        const { userId, isTermsAccepted }: any = await getUserReportTypes(
          res.user.uid
        );
        localStorage.setItem("ScalarIdToken", res.idToken);
        localStorage.setItem("ScalarUserId", userId);
        localStorage.setItem("ScalarFirebaseUid", res.user.uid);
        localStorage.setItem("ScalarUserEmail", res.user.email);
        localStorage.setItem("ScalarUserName", res.user.displayName);
        localStorage.setItem("ScalarLanguage", "nl");
        localStorage.setItem("isJoyrideDisplayed", "false")
        if (isTermsAccepted) {
          localStorage.setItem("showTermsModal", "false");
        } else {
          localStorage.setItem("showTermsModal", "true");
        }
        message.success(t("Login successful"));
        router.push("/home");
      }
    } catch (error: any) {
      if (error.message === "Missing or insufficient permissions.") {
        message.error(t("No account found with this email address."));
      }
      if (error.message === "User not found.") {
        message.error(t("No account found with this email address."));
      }
      if (error.message === "No allowed report types found for the user.") {
        message.error(t("No allowed report types found for the user."));
      }
    }
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmailError("");
    setEmail(e.target.value);
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPasswordError("");
    setPassword(e.target.value);
  };

  const handleResetPassword = async () => {
    if (!email) {
      setEmailError(t("Email required"));
      return;
    }
    setLoading(true);
    const res = await forgotPasswordWithFirebase(email);
    if (res) {
      setLoading(false);
      message.success(t("Password reset email sent"));
    } else {
      setLoading(false);
      message.error(t("Error sending password reset email."));
    }
  };

  return (
    <div className="fixed w-full h-full flex gap-7 justify-between items-center text-black p-[30px]">
      <div className="relative w-[50%] h-[100vh] flex items-center justify-center">
        {!isForgotPassword ? (
          <div className="flex flex-col justify-center ml-[100px]">
            <h1
              className={`${OpenSans.className} text-[40px] font-extrabold relative z-10 text-center`}
            >
              {t("Sign In")}
            </h1>
            <Button
              icon={
                googleHovered ? (
                  <GoogleOutlined
                    style={{ fontSize: "24px", color: "white" }}
                  />
                ) : (
                  <Image
                    src="/images/login/google.png"
                    alt="google"
                    width={24}
                    height={24}
                  />
                )
              }
              type="primary"
              className={`${OpenSans.className} w-[380px] border-[#B4B4B4] border-opacity-30 mt-[20px] bg-white text-black h-[53px] `}
              onMouseEnter={handleGoogleMouseEnter}
              onMouseLeave={handleGoogleMouseLeave}
              onClick={handleSignInWithGoogle}
            >
              {t("Sign in with Google")}
            </Button>
            <Button
              icon={
                appleHovered ? (
                  <AppleIcon style={{ fontSize: "29px", color: "white" }} />
                ) : (
                  <AppleIcon style={{ fontSize: "29px", color: "black" }} />
                )
              }
              type="primary"
              className={`${OpenSans.className} w-[380px] border-[#B4B4B4] border-opacity-30 mt-[10px] bg-white text-black h-[53px] `}
              onMouseEnter={handleAppleMouseEnter}
              onMouseLeave={handleAppleMouseLeave}
              onClick={handleSignInWithApple}
            >
              {t("Sign in with Apple")}
            </Button>
            <Button
              icon={
                microsoftHovered ? (
                  <MicrosoftIcon style={{ fontSize: "24px", color: "white" }} />
                ) : (
                  <Image
                    src="/images/login/microsoft.svg"
                    alt="micro"
                    width={24}
                    height={24}
                  />
                )
              }
              type="primary"
              className={`${OpenSans.className} w-[380px] border-[#B4B4B4] border-opacity-30 mt-[10px] bg-white text-black h-[53px] `}
              onMouseEnter={handleMicrosoftMouseEnter}
              onMouseLeave={handleMicrosoftMouseLeave}
              onClick={handleSignInWithMicrosoft}
            >
              {t("Sign in with Microsoft")}
            </Button>
            <div className="flex mt-[25PX] mb-[25px]">
              <hr className="flex-1 border-t-1 border-[#AAAAAA] opacity-30 mt-[10px] mx-16"></hr>
              <span className={`${OpenSans.className} text-black `}>
                {t("OR")}
              </span>
              <hr className="flex-1 border-t-1 border-[#AAAAAA] opacity-30 mt-[10px] mx-16"></hr>
            </div>
            <div className="relative mb-[20px]">
              <Input
                type="email"
                id="email"
                value={email}
                onChange={handleEmailChange}
                className={`border bg-opacity-5 w-[380px] h-[53px] ${
                  emailError
                    ? "border-red-500 bg-red-300"
                    : "border-[#2F80ED] border-opacity-30 bg-[#2F80ED]"
                }`}
                placeholder={t("Email")}
                prefix={
                  <Image
                    src={
                      emailFocus
                        ? "/images/login/mailFocus.svg"
                        : "/images/login/mail.svg"
                    }
                    alt="mail"
                    width={22}
                    height={22}
                    className="mr-[10px]"
                  />
                }
                onFocus={() => setEmailFocus(true)}
                onBlur={() => setEmailFocus(false)}
              />
              {emailError && (
                <p className="text-red-500 text-[12px] absolute">
                  {emailError}
                </p>
              )}
            </div>
            <div className="relative">
              <Input.Password
                id="password"
                value={password}
                onChange={handlePasswordChange}
                className={`border bg-opacity-5 w-[380px] h-[53px] ${
                  passwordError
                    ? "border-red-500 bg-red-300"
                    : "border-[#2F80ED] border-opacity-30 bg-[#2F80ED]"
                }`}
                onPressEnter={handleLogin}
                onFocus={() => setPasswordFocus(true)}
                onBlur={() => setPasswordFocus(false)}
                placeholder={t("Password")}
                prefix={
                  <Image
                    src={
                      passwordFocus
                        ? "/images/login/passwordFocus.svg"
                        : "/images/login/password.svg"
                    }
                    alt="password"
                    width={22}
                    height={22}
                    className="mr-[10px]"
                  />
                }
                iconRender={(visible) => {
                  // Disable visibility icon if password is empty
                  if (!password) {
                    return (
                      <EyeInvisibleOutlined
                        style={{ fontSize: 18, color: "#b8b8b8" }}
                      />
                    );
                  }

                  return visible ? (
                    <EyeOutlined style={{ fontSize: 18, color: "#707070" }} />
                  ) : (
                    <EyeInvisibleOutlined
                      style={{ fontSize: 18, color: "#b8b8b8" }}
                    />
                  );
                }}
                // onChange={(e) => setPassword(e.tarrget.value)}
              />
              {passwordError && (
                <p className="text-red-500 text-[12px] absolute">
                  {passwordError}
                </p>
              )}
            </div>
            <span
              className={`${OpenSans.className} text-black text-center mt-[25px] mb-[25px]`}
            >
              <Button
                onClick={() => {
                  setEmailError("");
                  setPassword("");
                  setPasswordError("");
                  setIsForgotPassword(true);
                }}
                className="border-none text-[15px] font-[500]"
                disabled={loading}
              >
                {t("Forgot Password?")}
              </Button>
            </span>

            <Button
              onClick={handleLogin}
              type="primary"
              className={`${OpenSans.className} custom-button w-[380px] text-[15px] border-[#B4B4B4] border-opacity-30 bg-[#2F80ED] text-white h-[53px]`}
              disabled={loading}
            >
              {loading ? <SignInLoader /> : t("Sign In")}
            </Button>
          </div>
        ) : (
          <div className="h-full flex flex-col justify-center ml-[100px]">
            <Button
              onClick={() => {
                setIsForgotPassword(false);
              }}
              className="border-none w-[40px] h-[40px] p-0 relative bottom-12"
            >
              <Image
                src="/images/login/chevronLeft.svg"
                width={30}
                height={30}
                alt="back"
                className=""
              />
            </Button>
            <h1
              className={`${OpenSans.className} text-[35px] font-bold relative z-10 text-center mb-16`}
            >
              {t("Forgot Password?")}
            </h1>
            <div className="flex flex-col items-center">
              <div className="relative mb-[10px]">
                <Input
                  type="email"
                  id="email"
                  value={email}
                  onChange={handleEmailChange}
                  className={`border bg-opacity-5 w-[380px] h-[53px] ${
                    emailError
                      ? "border-red-500 bg-red-300"
                      : "border-[#2F80ED] border-opacity-30 bg-[#2F80ED]"
                  }`}
                  placeholder={t("Email")}
                  prefix={
                    <Image
                      src={
                        emailFocus
                          ? "/images/login/mailFocus.svg"
                          : "/images/login/mail.svg"
                      }
                      alt="mail"
                      width={22}
                      height={22}
                      className="mr-[10px]"
                    />
                  }
                  onFocus={() => setEmailFocus(true)}
                  onBlur={() => setEmailFocus(false)}
                />
                {emailError && (
                  <p className="text-red-500 text-[12px] absolute">
                    {emailError}
                  </p>
                )}
              </div>
              <div className="relative mb-[20px]">
                <Button
                  onClick={() => setIsForgotPassword(false)}
                  className="border-none text-[15px] font-[500] rlative left-[8px]"
                  disabled={loading}
                >
                  {t("Sign In")}
                </Button>
              </div>
              <Button
                onClick={handleResetPassword}
                type="primary"
                className={`${OpenSans.className} custom-button w-[380px] text-[15px] border-[#B4B4B4] border-opacity-30 bg-[#2F80ED] text-white h-[53px]`}
                disabled={loading}
              >
                {loading ? <SignInLoader /> : t("Send Reset Link")}
              </Button>
            </div>
          </div>
        )}

        <div className="absolute inset-0 z-0 flex items-center justify-center pointer-events-none">
          {/* <Image
            src="/images/login/bubbleImage.svg"
            alt="bubbleImage"
            width={500}
            height={500}
            className="w-[150vw] h-[95vh] pt-[30px]"
          /> */}
        </div>
      </div>
      <div className="relative w-[50%]">
        <Image
          src="/images/login/bgImage@.svg"
          alt="bgImage"
          width={600}
          height={600}
          className="relative h-[95vh] w-[100vw]"
        />
        <div className="absolute inset-0 flex flex-col items-center justify-center p-4 mb-[150px]">
          <Image
            src="/images/login/loginSideLogo.svg"
            alt="logo"
            width={250}
            height={80}
          />
        </div>
      </div>
    </div>
  );
};

export default Login;
