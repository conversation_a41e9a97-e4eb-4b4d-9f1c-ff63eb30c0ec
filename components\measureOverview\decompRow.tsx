import React from "react";
import { Draggable } from "@hello-pangea/dnd";
import StrictModeDroppable from './StrictModeDroppable';
import MeasureRow from "./measureRows";
import { Open_Sans } from "next/font/google";

const OpenSans = Open_Sans({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

interface DecompositionRowProps {
  group: any;
  onDecompClick: (decompId: string) => void;
  onFindingClick: (findingId: string, parentId: string) => void;
  onImageClick: (src: string, isVideo: boolean) => void;
  groupedFindings: any[];
  maxFieldCount: number;
  fieldNames: string[];
}

const DecompositionRow: React.FC<DecompositionRowProps> = ({
  group,
  onDecompClick,
  onFindingClick,
  onImageClick,
  groupedFindings,
  maxFieldCount,
  fieldNames
}) => {
  // Find the decomposition data from groupedFindings
  const decompData = groupedFindings.find(item => item.id === group.id);
  
  // Get the decomposition fields
  const decompFields = decompData?.parent?.fields ? JSON.parse(decompData.parent.fields) : [];
  
  // Get media for the decomposition
  const decompMedia = decompData?.parent?.media || [];
  
  return (
    <div className="decomp-group border rounded-md mt-2 mb-2 mx-2 overflow-hidden">
      {/* Decomposition Row */}
      <div 
        className={`px-2 py-1 bg-gray-100 cursor-pointer hover:bg-gray-200 ${OpenSans.className}`}
        onClick={() => onDecompClick(group.id)}
      >
        <div className=" font-medium flex items-center">
          {/* Show decomposition images if available */}
          <span className="">{group.title}</span>
        </div> {/* Empty space for decomposition row */}
      </div>

      {/* Findings for this decomposition */}
      <StrictModeDroppable droppableId={group.id} type="task">
        {(provided) => (
          <div
            {...provided.droppableProps}
            ref={provided.innerRef}
            className="findings-list"
          >
            {group.measures.map((measure: any, index: number) => (
              <Draggable
                key={measure.id}
                draggableId={measure.id}
                index={index}
              >
                {(provided, snapshot) => (
                  <div
                    // ref={provided.innerRef}
                    // {...provided.draggableProps}
                    // {...provided.dragHandleProps}
                    // style={{
                    //   ...provided.draggableProps.style,
                    //   cursor: 'grab'
                    // }}
                    // className={snapshot.isDragging ? 'bg-blue-50 shadow-md rounded' : ''}
                  >
                    <MeasureRow
                      finding={measure}
                      parentId={group.id}
                      decompFields={decompFields}
                      onFindingClick={onFindingClick}
                      groupedFindings={groupedFindings}
                      maxFieldCount={maxFieldCount}
                      fieldNames={fieldNames}
                    />
                  </div>
                )}
              </Draggable>
            ))}
            {provided.placeholder}
          </div>
        )}
      </StrictModeDroppable>
    </div>
  );
};

export default DecompositionRow; 