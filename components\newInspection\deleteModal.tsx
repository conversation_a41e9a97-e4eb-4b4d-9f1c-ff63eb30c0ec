import React, { useState } from "react";
import { Open_Sans } from "next/font/google";
import { useTranslation } from "react-i18next";
import { Button, message } from "antd";
import { deleteInspections } from "@/src/services/newInspection.api";

const OpenSans = Open_Sans({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

const Modal = ({ modalTitle, setIsModalOpen, handleDelete, groupId, deleteId, isDecomposition }: any) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-10 animate-fadeIn">
      <div
        className={`text-center bg-white px-6 pt-4 pb-6 rounded-xl flex justify-center items-center text-black ${OpenSans.className} animate-slideIn relative`}
      >
        <button
          type="button"
          className="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center absolute top-4 right-4"
          onClick={() => setIsModalOpen(false)}
        >
          <svg
            className="w-3 h-3"
            aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 14 14"
          >
            <path
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
            />
          </svg>
          <span className="sr-only">Close modal</span>
        </button>
        <div>
          <h1 className="mt-6 text-left text-[25px] leading-[52.08px] font-[500]">
            {t("Delete")} {t(modalTitle)}!
          </h1>
          <p className="text-[18px] text-left">
            {t("Are you sure you want to")} <br />
            {t("delete")}?
          </p>
          <div>
            <div className="text-center flex justify-between gap-4 mt-6">
            <button
                type="button"
                className="w-[50%] h-[45px] text-[14px] text-[#FF9200] border border-[#FF9200] rounded-lg bg-[#ff910020] hover:bg-[#ff910015] transition-all"
                onClick={() => setIsModalOpen(false)}
              >
                {t("Cancel")}
              </button>
              <Button
                className="w-[50%] h-[45px] text-[14px] text-white border border-[#FF9200] rounded-lg bg-[#FF9200] hover:bg-[#ff9100d0] transition-all"
                style={{
                  backgroundColor: "#FF9200",
                  color: "white",
                  borderColor: "transparent",
                }}
                onClick={async () => {
                  setLoading(true);
                  await handleDelete(deleteId, groupId, isDecomposition)
                  setLoading(false);
                  setIsModalOpen(false)
                }}
                loading={loading}
              >
                {t("Delete")}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Modal;
