"use client";
import React, { useEffect, useState, useRef } from "react";
import { Open_Sans } from "next/font/google";
import LoadingCards from "./loadingCards";
import {
  updateFindingDetails,
  fetchFinding,
  fetchReportTypeId,
  fetchParentAPIValue,
} from "@/src/services/newFindings.api";
import { Timestamp } from "firebase/firestore";
import { useRouter } from "next/navigation";
import { Button, message, Modal, Popconfirm } from "antd";
import { useFindingContext } from "@/context/findingContext";
import { useTranslation } from "react-i18next";
import { handleChangeLanguage } from "@/context/LanguageContext";
import Media from "@/components/mediaModal/mediaModal";
import axios from "axios";
import Suggestions from "./suggestions";
import {
  deleteFinding,
  updateFinfingOrder,
  fetchTextSuggestions
} from "@/src/services/newInspection.api";
import { handleUpload } from "@/src/services/inspectionDetails.api";
import { createDBFieldObj } from "@/src/services/fieldsMapping";

const OpenSans = Open_Sans({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

const EditMeasureModal = ({
  isModalOpen,
  setIsModalOpen,
  isMeasureUpdate,
  setGroupedFindings,
  groupedFindings,
  selectedMeasure,
  setOrderArray,
  orderArray,
  setRefresh
}: any) => {
  const router = useRouter();
  const { t } = useTranslation();
  const { setImgSource, setNewAddedId, wholeGroup } = useFindingContext();

  const [suggestionResponse, setSuggestionResponse] = useState(null);
  const [editableFindings, setEditableFindings] = useState<any>([]);
  const [reportTypeId, setReportTypeId] = useState<any>("");
  const [UpdateInspection, setUpdateInspection] = useState<any>(null);
  const [parentFindingId, setParentFindingId] = useState("");

  const [showMedia, setShowMedia] = useState(false);
  const [mediaSource, setMediaSource] = useState("");
  const [mediaType, setMediaType] = useState("");

  const [loader, setLoader] = useState(false);
  const [loading, setLoading] = useState(false);
  const [apiLodaer, setApiLodaer] = useState(false);
  const [deleteLoader, setDeleteLoader] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const { isLoadingCard, setIsLoadingCard } = useFindingContext();
  const [textSuggestionLoader, setTextSuggestionLoader] = useState(false);

  const [imageUrl, setImageUrl] = useState<any>("");
  const [fieldImage, setFieldImage] = useState<any>(null);

  // Function for fetching reportTypeId
  const getReportTypeId = async (id: any) => {
    setLoader(true);
    const res: any = await fetchReportTypeId(id);
    if (res) {
      localStorage.setItem("reportTypeId", res);
      setReportTypeId(res);
      setLoader(false);
    } else {
      setLoader(false);
      message.error(t("Error fetching Inspection types."));
    }
  };

  // Function for Update Existing Measures
  const updateMeasures = async (id: string, obj: any) => {
    try {
      setIsLoadingCard(true);
      const res: any = await fetchFinding(id);
      if (res) {
        setUpdateInspection(res);
        setParentFindingId(res?.parent_finding_id);
        const variables = await JSON.parse(res.fields);

        if (variables) {
          const image_urls = res.media
            .filter((item: any) => !item.isVideo)
            .map((item: any) =>
              item?.resize_url ? item.resize_url : item.url
            );

          const apiRes: any = {
            image_urls: image_urls,
            parent_finding: res?.parent_finding_id,
            suggestions: {
              fields: variables,
              finding_type: res?.finding_type.id,
              title_fields: res?.title_fields,
            },
          };

          const image_field = variables.find((field: any) => field.type === "image_field");
          if (image_field) {
            setImageUrl(image_field.suggested_value);
          }

          setSuggestionResponse(apiRes);
          variables.forEach((item: any) => {
            if (obj.hasOwnProperty(item.name)) {
              item.suggested_value = obj[item.name];
            }
          });
          setEditableFindings(variables);
          setIsLoadingCard(false);
        }
      }
    } catch (error) {
      setIsLoadingCard(false);
      if (axios.isCancel(error)) {
        setIsLoadingCard(true);
      } else {
        message.error(t("Something went wrong, try again later!"));
      }
      setLoader(false);
    }
  };

  const hasRunRef = useRef(false);

  useEffect(() => {
    localStorage.removeItem("resize_media");
    if (hasRunRef.current) return; // Prevent the effect from running again

    hasRunRef.current = true; // Mark as run

    const language: any = localStorage.getItem("ScalarLanguage");
    handleChangeLanguage(language);
    localStorage.removeItem("ViewInspectionDetails");
    const inspection_id = localStorage.getItem("ScalarInspectionId");
    setIsLoadingCard(false);
    getReportTypeId(inspection_id);

    if (isMeasureUpdate) {
      const json = JSON.parse(selectedMeasure?.fields);
      updateMeasures(selectedMeasure.id, json);
    }
  }, []);

  const getTextSuggestions = async (suggestionObj: any) => {
    const inspectionTypeId: any = localStorage.getItem("reportTypeId");
    const inspectionId: any = localStorage.getItem("ScalarInspectionId");
    const findingTypeId: any = process.env.NEXT_MEASURE_ID;
    // const parentFindingId: any = parent_finding_id;
    let payload: any;

    const decompMedia = groupedFindings?.find((group: any) =>
      group?.decompositionItem?.find(
        (item: any) => item?.parent_finding_id === parentFindingId
      )
    )?.parent?.media;

    const image_urls = decompMedia
      ?.filter((media: any) => !media.isVideo)
      ?.map((media: any) => media.url) || [];

    payload = {
      image_urls: image_urls,
      parent_finding: parentFindingId,
      suggestions: suggestionObj.suggestions,
    };

    try {
      setTextSuggestionLoader(true);
      const res = await fetchTextSuggestions(
        inspectionTypeId,
        inspectionId,
        findingTypeId,
        payload
      );

      if (res) {
        const textNumberFields = res?.suggestions?.fields.filter(
          (field: any) => field.type === "text" || field.type === "number"
        );
        const fieldsAfterUpdate = suggestionObj.suggestions.fields.map(
          (field: any) => {
            const textNumField = textNumberFields.find(
              (f: any) => f.id === field.id
            );
            if (textNumField) {
              return {
                ...field,
                suggested_value: textNumField.suggested_value,
              };
            } else {
              return field;
            }
          }
        );
        setSuggestionResponse(res);
        setEditableFindings(fieldsAfterUpdate);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setTextSuggestionLoader(false);
    }
  };

  const createfieldvaluesObject = (arr: any) => {
    const obj: any = {};

    arr.forEach((item: any) => {
      if (item.id && item.suggested_value !== undefined) {
        obj[item.name] = item.suggested_value;
      }
    });

    return obj;
  };

  useEffect(() => {
    setNewAddedId("");
    setImgSource(null);
  }, []);

  // Function for update existing measure in the database
  const handleEditFinding = async () => {
    // Check if required fields are filled
    const hasEmptyRequiredField = editableFindings.some(
      (field: any) =>
        field.required &&
        (field.suggested_value === null ||
          field.suggested_value === undefined ||
          field.suggested_value === "" ||
          (field.type === "multipicker" &&
            Array.isArray(field.suggested_value) &&
            field.suggested_value.length === 0))
    );

    if (hasEmptyRequiredField) {
      message.error(t("Please fill all required fields"));
      return;
    }

    const imageField = editableFindings.find((field: any) => field.type === "image_field");

    let newFields = [...editableFindings];

    if (imageField && fieldImage) {
      const url = await handleUpload(fieldImage, "finding");
      imageField.suggested_value = url;
      imageField.media = {
        url: url,
        resize_url: url,
        isVideo: false,
      }
      newFields = newFields.map((field: any) => {
        if (field.id === imageField.id) {
          return imageField;
        }
        return field;
      });
    }

    setLoading(true);
    const field_values = await createfieldvaluesObject(newFields);
    const timeStamp = Timestamp.now();

    // Create base finding details
    const findingDetails = {
      fields: JSON.stringify(newFields),
      field_values: field_values,
      updated_at: timeStamp,
      approved: true,
    };

    try {
      const inspectionId: any = localStorage.getItem("ScalarInspectionId");
      const inspectionType: any = localStorage.getItem(
        "ScalarInspectionTypeId"
      );

      // Update the finding details
      const res: any = await updateFindingDetails(
        findingDetails,
        selectedMeasure.id
      );

      if (res) {
        setGroupedFindings((prev: any) =>
          prev.map((item: any) =>
            item.id === wholeGroup.id
              ? {
                  ...item,
                  decompositionItem: item.decompositionItem.map((decomp: any) =>
                    decomp.id === selectedMeasure.id
                      ? { ...decomp, fields: JSON.stringify(editableFindings) }
                      : decomp
                  ),
                }
              : item
          )
        );
        if (UpdateInspection?.parent_finding_id !== null) {
          await updateParentAPIField(
            UpdateInspection?.parent_finding_id,
            inspectionType,
            inspectionId
          );
        }
        setIsModalOpen(false);
        message.success(t("Successfully Updated!"));
      } else {
        throw new Error("Update failed");
      }
    } catch (error) {
      message.error(t("Something went wrong, try again later!"));
    } finally {
      setLoading(false);
    }
  };

  const updateParentAPIField = async (
    parent_finding_id: string,
    inspectionType: string,
    inspection_id: string
  ) => {
    const res: any = await fetchFinding(parent_finding_id);
    if (res) {
      const findingType = res?.finding_type?.id;
      const fields = await JSON.parse(res.fields);

      if (fields) {
        const parentAPIField = fields.find(
          (field: any) => field.type === "parent_api"
        );

        if (parentAPIField) {
          const payload = {
            parent_finding_id: parent_finding_id,
          };
          const res = await fetchParentAPIValue(
            inspectionType,
            inspection_id,
            findingType,
            parentAPIField?.id,
            payload
          );

          if (res) {
            const value = parseInt(res?.result?.value);

            const updatedFields = fields.map((field: any) => {
              if (field.type === "parent_api") {
                return {
                  ...field,
                  suggested_value: value,
                };
              } else {
                return field;
              }
            });

            const updatedFieldValues = await createfieldvaluesObject(
              updatedFields
            );

            const findingDetails: any = {
              fields: JSON.stringify(updatedFields),
              field_values: updatedFieldValues,
            };

            await updateFindingDetails(findingDetails, parent_finding_id);
          }
        }
      }
    }
  };

  const handleDelete = async () => {
    try {
      setDeleteLoader(true);
      const res: any = await deleteFinding(selectedMeasure.id);

      if (res) {

        const inspectionId: any = localStorage.getItem("ScalarInspectionId");
        const inspectionType: any = localStorage.getItem(
          "ScalarInspectionTypeId"
        );

        await updateParentAPIField(
          UpdateInspection?.parent_finding_id,
          inspectionType,
          inspectionId
        );

        // 1. Remove the measure from groupedFindings
        setGroupedFindings((prev: any) => {
          const groupId = localStorage.getItem("parentId");
          return prev.map((item: any) => {
            if (item.id === groupId) {
              return {
                ...item,
                decompositionItem: item.decompositionItem.filter(
                  (decomp: any) => decomp.id !== selectedMeasure.id
                ),
              };
            }
            return item;
          });
        });

        setOrderArray((prev: any) =>
          prev.filter((item: any) => item.id !== selectedMeasure.id)
        );

        // 3. Update the order in localStorage and database
        const newOrderIds = orderArray
          .filter((item: any) => item.id !== selectedMeasure.id)
          .map((item: any) => item.id);

        localStorage.setItem("findingOrder", JSON.stringify(newOrderIds));
        updateFinfingOrder(newOrderIds);

        setRefresh((prev: any) => !prev)

        message.success(t("Successfully Deleted!"));
        setIsModalOpen(false);
      } else {
        message.error(t("Something went wrong, try again later!"));
      }
    } catch (error) {
      message.error(t("Something went wrong, try again later!"));
    } finally {
      setDeleteLoader(false);
    }
  };

  return (
    <>
      <Modal
        centered
        title={
          <h1 className="text-[20px] leading-[24px] font-[500] px-3">
            {isEditMode ? t("Edit Measure") : t("Measure")}
          </h1>
        }
        open={isModalOpen}
        onOk={handleEditFinding}
        onCancel={() => setIsModalOpen(false)}
        okText="Approve"
        okButtonProps={{
          loading: loading,
          disabled: loader,
          className: "h-[40px] text-[16px]",
        }}
        footer={[
          isEditMode ? (
            <Popconfirm
              key="delete"
              title={t("Delete Measure")}
              placement="topRight"
              description={t("Are you sure you want to delete?")}
              onConfirm={handleDelete}
              okText={t("Yes")}
              cancelText={t("No")}
              okButtonProps={{
                loading: deleteLoader,
                danger: true,
              }}
            >
              <Button
                className="h-[40px] text-[16px] leading-[24px] font-[400] hover:text-[#F0142F] hover:border-[#FF7875] custom-cancel-button"
              >
                {t("Delete")}
              </Button>
            </Popconfirm>
          ) : (
            <Button
              key="edit"
              onClick={() => setIsEditMode(true)}
              className="h-[40px] text-[16px] leading-[24px] font-[400]"
              disabled={loader}
            >
              {t("Edit")}
            </Button>
          ),
          <Button
            key="primary"
            type="primary"
            onClick={
              isEditMode ? handleEditFinding : () => setIsModalOpen(false)
            }
            loading={loading}
            disabled={loader || apiLodaer || textSuggestionLoader}
            className="h-[40px] text-[16px] custom-button-disable"
          >
            {isEditMode ? t("Save") : t("Close")}
          </Button>,
        ]}
        width="80vw"
      >
        <div className="max-h-[75vh] overflow-y-auto scrollbar my-3 px-4 flex flex-col box-border">
          {isLoadingCard ? (
            <LoadingCards />
          ) : (
            <Suggestions
              {...{
                editableFindings,
                setEditableFindings,
                isAddingMeasure: false,
                isMeasureUpdate,
                isUpdate: true,
                suggestionResponse,
                setSuggestionResponse,
                textSuggestionLoader,
                getTextSuggestions,
                apiLodaer,
                setApiLodaer,
                isEditMode,
                setIsEditMode,
                fieldImage,
                setFieldImage,
                imageUrl,
                setImageUrl,
                parentFindingId
              }}
            />
          )}
        </div>
      </Modal>
      <Media
        mediaType={mediaType}
        source={mediaSource}
        showMedia={showMedia}
        setShowMedia={setShowMedia}
      />
    </>
  );
};

export default EditMeasureModal;
