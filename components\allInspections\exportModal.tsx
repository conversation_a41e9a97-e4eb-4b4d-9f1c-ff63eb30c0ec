'use client';

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Open_Sans } from 'next/font/google';
import { exportInspection, CompleteInspections, TodoInspections } from '@/src/services/newInspection.api';
import { Button, Checkbox, Modal as AntdModal, message } from 'antd';

const OpenSans = Open_Sans({ weight: '400', subsets: ['latin'] });

interface ExportModalProps {
  isModalOpen: boolean;
  setIsModalOpen: (open: boolean) => void;
  setMenu: (open: boolean) => void;
  selectedInspections: string[];
  searchedInspections: any[];
  setSearchedInspections: (value: any[]) => void;
}

const ExportModal: React.FC<ExportModalProps> = ({
  isModalOpen,
  setIsModalOpen,
  selectedInspections,
  searchedInspections,
  setSearchedInspections,
  setMenu
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [complete, setComplete] = useState(false);
  const [exportInsp, setExportInsp] = useState(false);

  const exportInspections = async () => {
    try {
      if (selectedInspections.length > 0) {
        const payload = {
          user_id: localStorage.getItem('ScalarUserId'),
          inspection_ids: selectedInspections,
        };
        const res = await exportInspection(payload);

        if (res?.inspection_ids?.length === selectedInspections.length) {
          message.success(t('Successfully exported all inspections.'));
          return true;
        } else {
          message.warning(t('Some inspections failed to export.'));
          return false;
        }
      }
    } catch (error) {
      message.error(t('Something went wrong while exporting, try again later.'));
      return false;
    }
  };

  const markCompleteInspections = async () => {
    try {
      if (selectedInspections.length > 0) {
        const res = await CompleteInspections(selectedInspections);
        if (res) {
          const updated = searchedInspections.map((insp: any) =>
            selectedInspections.includes(insp.id)
              ? { ...insp, isCompleted: true }
              : insp
          );
          setSearchedInspections(updated);
          return true;
        }
      }
      return false;
    } catch (error) {
      message.error(t('Something went wrong, try again later!'));
      return false;
    }
  };

  const markTodoInspections = async () => {
    try {
      if (selectedInspections.length > 0) {
        const res = await TodoInspections(selectedInspections);
        if (res) {
          const updated = searchedInspections.map((insp: any) =>
            selectedInspections.includes(insp.id)
              ? { ...insp, isCompleted: false }
              : insp
          );
          setSearchedInspections(updated);
          return true;
        }
      }
      return false;
    } catch (error) {
      message.error(t('Something went wrong, try again later!'));
      return false;
    }
  };

  const handleExport = async () => {
    setLoading(true);

    try {
      if (complete && !exportInsp) await markCompleteInspections();
      if (!complete && !exportInsp) await markTodoInspections();
      if (complete && exportInsp) {
        await markCompleteInspections();
        await exportInspections();
      }
      if (!complete && exportInsp) {
        await markTodoInspections();
        await exportInspections();
      }
    } finally {
      setLoading(false);
      setIsModalOpen(false);
      setMenu(false)
    }
  };

  return (
    <AntdModal
      open={isModalOpen}
      onCancel={() => setIsModalOpen(false)}
      footer={null}
      centered
      className={`${OpenSans.className} custom-export-modal`}
      width={350}
    >
      <div className="relative">

        <h1 className="mt-6 text-left text-[25px] leading-[52.08px] font-[600]">
          {t('Export')} {t('Inspections')}!
        </h1>
        <p className="text-[18px] text-left">
          {t('Are you sure you want to Export')} <br />
          {t('Inspections')}?
        </p>

        <div className="flex flex-col items-start gap-2 mt-4">
          <Checkbox onChange={(e) => setComplete(e.target.checked)}>
            {t('Mark inspections as completed')}
          </Checkbox>
          <Checkbox onChange={(e) => setExportInsp(e.target.checked)}>
            {t('Export inspections')}
          </Checkbox>
        </div>

        <div className="text-center flex justify-between gap-4 mt-10">
          <Button
            type="default"
            className="w-[50%] h-[45px] text-[14px] text-[#FF9200] border border-[#FF9200] bg-[#ff910020] hover:bg-[#ff910015]"
            onClick={() => setIsModalOpen(false)}
          >
            {t('Cancel')}
          </Button>
          <Button
            type="primary"
            className="w-[50%] h-[45px] text-[14px] border-none"
            style={{
              backgroundColor: '#FF9200',
              color: 'white',
            }}
            onClick={handleExport}
            loading={loading}
          >
            {t('Export')}
          </Button>
        </div>
      </div>
    </AntdModal>
  );
};

export default ExportModal;
