import React from "react";
import { Button } from "antd";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";
import { Open_Sans } from "next/font/google";

const OpenSans = Open_Sans({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

const AddMenuButtons = ({
  groupedFindings,
  openGroup,
  setIsAddMenuVisible,
  t,
}: any) => {
  const router = useRouter();

  const handleDecompositionItem = () => {
    localStorage.setItem("isFindingUpdate", "false");
    localStorage.setItem("parent_finding_id", "null");
    localStorage.setItem("isAddingMeasure", "false");
    localStorage.setItem("isMeasureUpdate", "false");
    localStorage.setItem("addNew", "true");
    const decompositionId: any = process.env.NEXT_DECOMP_ID
    localStorage.setItem("FindingTypeId", decompositionId);
    router.push("/newInspection/newFindings");
  };

  const handleFinding = () => {
    localStorage.setItem("isFindingUpdate", "false");
    localStorage.setItem("isMeasureUpdate", "false");
    localStorage.setItem("addNew", "true");
    localStorage.setItem("parent_finding_id", openGroup);
    localStorage.setItem("isAddingMeasure", "false");
    const findingId: any = process.env.NEXT_FINDING_ID
    localStorage.setItem("FindingTypeId", findingId);
    router.push("/newInspection/newFindings");
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
      className="p-2 absolute top-[110%] right-0 z-50 bg-white rounded-2xl flex flex-col gap-2 shadow-md border"
    >
      <Button
        onClick={handleDecompositionItem}
        type="primary"
        className={`${OpenSans.className} w-full custom-button px-4 py-5 rounded-xl text-[15px] border-opacity-30 bg-[#2F80ED] text-white`}
      >
        {t("Decomposition item")}
      </Button>
      {groupedFindings.length > 0 && (
        <Button
          onClick={handleFinding}
          type="primary"
          className={`${OpenSans.className} w-full custom-button px-4 py-5 rounded-xl text-[15px] border-opacity-30 bg-[#2F80ED] text-white `}
        >
          {t("Finding")}
        </Button>
      )}
      <Button
        onClick={() => {
          setIsAddMenuVisible(false);
        }}
        type="primary"
        style={{
          backgroundColor: "#ff91000a",
          color: "#FF9200",
        }}
        className={`${OpenSans.className} w-full custom-button px-4 py-5 rounded-xl text-[15px] text-[#FF9200] border border-[#FF9200] bg-[#ff91000a] hover:bg-[#ff910015]`}
      >
        {t("Cancel")}
      </Button>
    </motion.div>
  );
};

export default AddMenuButtons;
