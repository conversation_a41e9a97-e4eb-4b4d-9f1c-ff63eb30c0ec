import { db } from "@/firebase.config";
import {
  writeBatch,
  doc,
  getDoc,
  arrayRemove,
  query,
  collection,
  where,
  orderBy,
  limit,
  startAfter,
  getDocs,
} from "firebase/firestore";

export const deleteInspections = async (
  selectedInspections: { id: string; project: { id: string } }[]
): Promise<boolean> => {
  try {
    const batch = writeBatch(db);

    for (const inspection of selectedInspections) {
      const {
        id: inspectionId,
        project: { id: projectId },
      } = inspection;

      // Get the project document reference
      const projectDocRef = doc(db, "projects", projectId);

      // Verify project exists
      const projectSnapshot = await getDoc(projectDocRef);
      if (!projectSnapshot.exists()) {
        continue; // Skip this inspection
      }

      const inspectionReference = doc(db, "inspection", inspectionId);

      // Add project update to the batch
      batch.update(projectDocRef, {
        inspections: arrayRemove(inspectionReference),
      });

      // Add inspection deletion to the batch
      batch.delete(inspectionReference);
    }

    // Execute all operations atomically
    await batch.commit();

    return true;
  } catch (error) {
    throw error;
  }
};

export const getInspections = async ({
  search = "",
  isForFirst = true,
  lastDocumentName = null,
  lastDocumentQRCode = null,
  limitValue = 10,
}: any) => {
  try {
    const clientId = localStorage.getItem("client");
    if (!clientId) throw new Error("Client ID not found");

    let inspectionList: any[] = [];
    let hasMoreName = false;
    let hasMoreQRCode = false;

    const baseFilters = [where("client", "==", clientId)];

    if (search) {
      // First query: search by `name`
      const nameQuery = query(
        collection(db, "inspection"),
        ...baseFilters,
        where("search_name", ">=", search),
        where("search_name", "<=", `${search}\uf8ff`),
        orderBy("name"),
        orderBy("created_at", "desc"),
        ...(isForFirst || !lastDocumentName ? [] : [startAfter(lastDocumentName)]),
        limit(limitValue)
      );

      // Second query: search by `qr_code`
      const qrQuery = query(
        collection(db, "inspection"),
        ...baseFilters,
        where("qr_code", ">=", search),
        where("qr_code", "<=", `${search}\uf8ff`),
        orderBy("qr_code"),
        orderBy("created_at", "desc"),
        ...(isForFirst || !lastDocumentQRCode ? [] : [startAfter(lastDocumentQRCode)]),
        limit(limitValue)
      );

      // Run both queries in parallel
      const [nameSnap, qrSnap] = await Promise.all([getDocs(nameQuery), getDocs(qrQuery)]);

      // Deduplicate and combine results
      const docsMap = new Map();
      nameSnap.docs.forEach((doc) => docsMap.set(doc.id, doc));
      qrSnap.docs.forEach((doc) => docsMap.set(doc.id, doc));

      const mergedDocs = Array.from(docsMap.values());

      inspectionList = mergedDocs.map((doc) => ({
        ...doc.data(),
        id: doc.id,
      }));

      hasMoreName = nameSnap.docs.length === limitValue;
      hasMoreQRCode = qrSnap.docs.length === limitValue;

      return {
        list: inspectionList,
        hasMore: hasMoreName || hasMoreQRCode, // Check if there's more in either query
        lastDocumentName: nameSnap.docs.length > 0 ? nameSnap.docs[nameSnap.docs.length - 1] : lastDocumentName,
        lastDocumentQRCode: qrSnap.docs.length > 0 ? qrSnap.docs[qrSnap.docs.length - 1] : lastDocumentQRCode,
      };
    } else {
      // No search term: simple paginated query
      const simpleQuery = query(
        collection(db, "inspection"),
        ...baseFilters,
        orderBy("created_at", "desc"),
        ...(isForFirst || !lastDocumentName ? [] : [startAfter(lastDocumentName)]),
        limit(limitValue)
      );

      const snap = await getDocs(simpleQuery);

      inspectionList = snap.docs.map((doc) => ({
        ...doc.data(),
        id: doc.id,
      }));

      hasMoreName = snap.docs.length === limitValue;

      return {
        list: inspectionList,
        hasMore: hasMoreName,
        lastDocumentName: snap.docs.length > 0 ? snap.docs[snap.docs.length - 1] : lastDocumentName,
        lastDocumentQRCode, // qr_code pagination state remains unchanged
      };
    }
  } catch (error: any) {
    console.error("inspectionError", error);
    return {
      list: [],
      hasMore: false,
      lastDocumentName: null,
      lastDocumentQRCode: null,
      error: error.message || "Unknown error",
    };
  }
};

export const getProjectNames = async (projectIds: string[]) => {
  try {
    const promises = projectIds.map(async (id) => {
      try {
        const ref = doc(db, "projects", id);
        const snap = await getDoc(ref);
        if (snap.exists()) {
          const data = snap.data();
          return [id, data?.name ?? data?.display_name ?? "-"] as [string, string];
        } else {
          return [id, "-"] as [string, string];
        }
      } catch (err) {
        console.error(`Error fetching project with id ${id}:`, err);
        return null; // Skip this id
      }
    });

    const results = await Promise.all(promises);
    const filtered = results.filter((item): item is [string, string] => !!item);

    return Object.fromEntries(filtered);
  } catch (error) {
    console.error('projectError', error);
  }
};
