import React, { useEffect, useState } from "react";
import { Open_Sans } from "next/font/google";
import { <PERSON>ltip, Spin, Popover, Image } from "antd";
import { LoadingOutlined } from "@ant-design/icons";
import { getMultiPickerSuggestedValue } from "../newInspection/utils";

const OpenSans = Open_Sans({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

interface FindingRowProps {
  finding: any;
  parentId: string;
  onFindingClick: (findingId: string, parentId: string) => void;
  onImageClick: (src: string, isVideo: boolean) => void;
  groupedFindings: any[];
  maxFieldCount: number;
  decompFields: any[];
  fieldNames: string[];
}

const FindingRow: React.FC<FindingRowProps> = ({
  finding,
  parentId,
  decompFields,
  onFindingClick,
  onImageClick,
  groupedFindings,
  maxFieldCount,
  fieldNames,
}) => {
  // Track loading state for each media item
  const [loadingStates, setLoadingStates] = useState<Record<number, boolean>>(
    finding.media?.reduce(
      (acc: Record<number, boolean>, _: any, idx: number) => {
        acc[idx] = true;
        return acc;
      },
      {}
    ) || {}
  );

  // Parse the finding fields
  const findingFields = finding.fields ? JSON.parse(finding.fields) : [];

  // Get media for the finding
  const findingMedia = finding.media || [];

  // Function to handle image load completion
  const handleImageLoad = (index: number) => {
    setLoadingStates((prev) => ({
      ...prev,
      [index]: false,
    }));
  };

  // Find the measures count for this finding
  const getMeasureCount = () => {
    // Find the group in groupedFindings
    const group = groupedFindings.find((g) => g.id === parentId);
    if (!group) return 0;

    // Count measures that have this finding as parent
    return group.decompositionItem.filter(
      (item: any) => item.parent_finding_id === finding.id
    ).length;
  };

  const measureCount = getMeasureCount();

  // Get field value by field name instead of index
  const getFieldValueByName = (fieldName: string) => {
    if (!findingFields || !findingFields.length) return null;

    const field = findingFields.find((f: any) => f.name === fieldName);
    if (
      !field ||
      field?.suggested_value === undefined ||
      field?.suggested_value === null
    ) {
      return null;
    }

    if (
      field?.type === "parent_api" ||
      field?.type === "api" ||
      field?.type === "text" ||
      field?.type === "number" ||
      field?.type === "calculation" ||
      field?.type === "date_picker"
    ) {
      if (
        typeof field?.suggested_value === "string" ||
        typeof field?.suggested_value === "number"
      ) {
        return {
          name: field?.name,
          value: field?.suggested_value || null,
        };
      } else {
        return {
          name: field?.name,
          value: JSON.stringify(field?.suggested_value) || null,
        };
      }
    }

    if (field?.type === "image_field") {
      return {
        name: field?.name,
        value: field?.suggested_value
          ? {
              src: field?.suggested_value,
              isImagePicker: true,
            }
          : null,
      };
    }

    if (field?.type === "multipicker") {
      return {
        name: field?.name,
        value: getMultiPickerSuggestedValue(field)?.toString() || null,
      };
    }

    if (field?.type === "picker") {
      if (Array.isArray(field?.options)) {
        return {
          name: field?.name,
          value: field?.options
            ?.find((option: any) => option.id === field?.suggested_value)
            ?.value?.toString(),
        };
      }
      if (typeof field?.options === "object") {
        const parentFieldValue = findingFields.find(
          (f: any) => f.id === field?.parent_field
        )?.suggested_value;
        if (parentFieldValue) {
          const optionsArray = field?.options[parentFieldValue];
          // if (!optionsArray) {
          //   console.log('fieldName', fieldName)
          //   console.log('parentFieldValue', parentFieldValue)
          //   console.log('finding=======', finding)
          //   console.log('field', field)
          // }
          return {
            name: field?.name,
            value: optionsArray
              ?.find((option: any) => option.id === field?.suggested_value)
              ?.value?.toString(),
          };
        } else {
          const decompositionParentValue = decompFields.find(
            (f: any) => f.id === field?.parent_field
          )?.suggested_value;
          if (decompositionParentValue) {
            const optionsArray = field?.options[decompositionParentValue];
            return {
              name: field?.name,
              value: optionsArray
                ?.find((option: any) => option.id === field?.suggested_value)
                ?.value?.toString(),
            };
          }
        }
      }
      return {
        name: field?.name,
        value: field?.suggested_value?.toString(),
      };
    }
  };

  return (
    <div
      className={`grid px-2 py-[2px] border-t hover:bg-gray-50 cursor-pointer ${OpenSans.className}`}
      style={{
        gridTemplateColumns: `minmax(80px, 0.8fr) minmax(40px, 40px) repeat(${
          maxFieldCount - 1
        }, minmax(20px, 0.7fr)) 80px`,
      }}
      onClick={() => onFindingClick(finding.id, parentId)}
    >
      <Popover
        title={finding.title}
        autoAdjustOverflow
        overlayInnerStyle={{
          maxWidth: "300px",
          maxHeight: "300px",
          overflow: "auto",
          textAlign: "center",
        }}
      >
        <div className="truncate pl-4">{finding.title}</div>
      </Popover>
      <div className="w-full flex items-center justify-center">
        {/* Show finding images if available */}
        <div className="flex gap-1 flex-wrap">
          {findingMedia
            .filter((media: any) => !media.isVideo)
            .slice(0, 1) // Only take the first image
            .map((media: any, idx: number) => (
              <div
                key={idx}
                className="w-4 h-4 bg-gray-100 relative overflow-hidden rounded"
                onClick={(e) => {
                  e.stopPropagation();
                  onImageClick(media.url, media.isVideo);
                }}
              >
                {/* Loading spinner */}
                {loadingStates[idx] && (
                  <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-60">
                    <Spin
                      indicator={
                        <LoadingOutlined style={{ fontSize: 10 }} spin />
                      }
                    />
                  </div>
                )}

                <img
                  src={media.resize_url || media.url}
                  alt="thumbnail"
                  className="w-full h-full object-cover"
                  onLoad={() => handleImageLoad(idx)}
                  onError={() => handleImageLoad(idx)}
                />
              </div>
            ))}
        </div>
      </div>

      {/* Render field values based on field names */}
      {fieldNames.map((fieldName, i) => {
        const fieldValue = getFieldValueByName(fieldName);

        if (fieldValue?.value) {
          if (fieldValue?.value?.isImagePicker) {
            return (
              <Image
                key={i}
                src={fieldValue?.value?.src}
                alt={"-"}
                width={30}
                height={30}
                className="w-[30px] h-[30px] rounded-md object-contain border cursor-pointer"
                preview={{ mask: false, toolbarRender: () => null }}
              />
            )
          }
          return (
            <Popover
              key={i}
              title={fieldName}
              content={fieldValue?.value || "-"}
              autoAdjustOverflow
              overlayInnerStyle={{
                maxWidth: "300px",
                maxHeight: "300px",
                overflow: "auto",
              }}
              arrow={false}
            >
              <div className="truncate text-left px-2 pt-[2px]">
                {fieldValue?.value || "-"}
              </div>
            </Popover>
          );
        } else {
          return (
            <div key={i} className="truncate text-left px-2 pt-[2px]">
              {fieldValue?.value || "-"}
            </div>
          );
        }
      })}

      <div className="text-center pt-[2px]">{measureCount || "-"}</div>
    </div>
  );
};

export default FindingRow;
