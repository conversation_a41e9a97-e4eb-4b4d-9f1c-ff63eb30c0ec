import { getAuth, onAuthStateChanged } from "firebase/auth";
import { fetch as axiosFetch } from "../libs/helpers";
import { doc, getDoc, updateDoc } from "firebase/firestore";
import { db } from "@/firebase.config";

const host = process.env.NEXT_HOST;

const auth = getAuth();

export async function fetchUserDetails() {
  try {
    const user = await new Promise((resolve, reject) => {
      const unsubscribe = onAuthStateChanged(auth, (user) => {
        unsubscribe(); // Unsubscribe from the listener once we get the user
        if (user) {
          resolve(user);
        } else {
          reject("No user is signed in.");
        }
      });
    });
    return user
  } catch (error) {
  }
}

export const getUserProfile = async (userId: any) => {
  const docRef = doc(db, "user", userId);
  const docSnap = await getDoc(docRef);

  if (docSnap.exists()) {
    const docData = docSnap.data();
    return docData;
  } else {
    return null;
  }
};

const tempOptionFields = {
  "suggestions": {
    "fields": [
      {
        "id": "element",
        "name": "Element",
        "options": [
          { "id": "A4.23", "value": "Vloeren" },
          { "id": "A4.24", "value": "Trappen en hellingen" },
          { "id": "A4.34", "value": "Balustrades en leuningen" }
        ],
        "recalculation_parents": [],
        "required": true,
        "suggested_value": "A4.23",
        "type": "picker",
        "conditional_field": false
      },
      {
        "id": "bouwdeel",
        "name": "Bouwdeel",
        "options": {
          "A4.23": [
            { "id": "A4.23.10", "value": "Onderconstructies" },
            { "id": "A4.23.20", "value": "Bordessen" }
          ],
          "A4.24": [
            { "id": "A4.24.10", "value": "Trappen" },
            { "id": "A4.24.20", "value": "Hellingen" },
            { "id": "A4.24.30", "value": "Ladders en klimijzers" }
          ],
          "A4.34": [
            { "id": "A4.34.10", "value": "Constructief algemeen" },
            { "id": "A4.34.20", "value": "Leuningen" }
          ]
        },
        "parent_field": "element",
        "recalculation_parents": [],
        "required": true,
        "suggested_value": null,
        "type": "picker",
        "conditional_field": true,
        "conditional_parent_field_id": "A4.34"
      }
    ],
    "finding_type": ""
  }
};

export const generateProfileFields = async (): Promise<any> => {
  const clientId = localStorage.getItem("client");
  const userId = localStorage.getItem("ScalarUserId");
  const res: any = await axiosFetch({
    url: `${host}/v3/clients/${clientId}/users/${userId}/template`,
    method: "POST",
  });

  return res?.suggestions;
};

export const updateProfileDetails = async (
  profileDetails: any,
  userId: string,
) => {
  const docRef = doc(db, "user", userId);
  await updateDoc(docRef, profileDetails);
};