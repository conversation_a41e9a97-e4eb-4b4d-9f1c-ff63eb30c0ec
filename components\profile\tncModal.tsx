import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, But<PERSON> } from "antd";
import { Open_Sans } from "next/font/google";

const OpenSans = Open_Sans({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

const TNCModal = ({
  isTermsModalVisible,
  handleTermsOk,
  handleTermsCancel,
  t,
}: any) => {

  return (
    <Modal
      title={
        <div className={`text-center text-[22px] ${OpenSans.className}`}>
          {t("Terms and Conditions")}
        </div>
      }
      open={isTermsModalVisible} // Changed from open to visible
      onOk={handleTermsOk}
      onCancel={handleTermsCancel}
      width={680}
      centered
      footer={null}
      className="custom-modal-sidebar"
      styles={{
        body: {
          maxHeight: "80vh",
          overflowY: "auto",
        },
      }}
    >
      <div className="h-full">
      <h2 className={`text-[18px] font-[500] text-justify my-4 ${OpenSans.className}`}>
        General terms and conditions Scalar B.V.
      </h2>
      <p className={`text-[16px] font-[500] text-justify mb-4 ${OpenSans.className}`}>
        1. Applicability
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        1.1. These general terms and conditions (hereinafter: Scalar terms and conditions) apply to all offers and agreements with respect to the application Scalar Inspect and related work and services provided by Scalar B.V. (hereinafter: Scalar).
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        1.2. The general terms and conditions of the Customer or other general or special terms and conditions of, or used by, the Customer do not apply and are expressly rejected.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        1.3. Should one or more provisions of these Scalar terms and conditions be null and void or voidable, this shall not affect the validity of the remaining provisions. In the event of nullity/annullability of any provision of the Scalar terms and conditions, the parties shall negotiate in good faith and attempt to reach agreement on a viable, alternative provision, reflecting Scalar&apos;s original intentions as much as possible, in order to replace the provision deemed null and void or voidable.
      </p>
      <p className={`text-[16px] font-[500] text-justify mb-4 ${OpenSans.className}`}>
        2. Definitions
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        2.1. Application: the Scalar Inspect application that provides support to inspectors in recording and reporting their inspection findings.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        2.2. AVG: General Data Protection Regulation.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        2.3. Client data: all information, data, files and other materials that the Customer or a User uploads, stores, transmits or otherwise processes through the Application and the other services provided by Scalar
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        2.4. Client Security Breach: a security incident, including a data breach, caused by or attributable to the Customer and/or one or more Users.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        2.5. Services: the services as described in article 3.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        2.6. Users: natural persons, being employees of the Customer, including temporary employees and secondees, who have been authorized by the Customer to use the Application via the Portal.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        2.7. User Question: a question from the Customer or a User about the use of the Application in cases not involving an Incident.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        2.8. Incident: the full or partial unavailability of the Application due to a cause attributable to Scalar, provided that shortcomings on the part of or force majeure on the part of Scalar&apos;s suppliers are not attributable to Scalar
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        2.9. License Period: the term of the agreement between Scalar and the Customer.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        2.10. Measurement Period: the period as of the effective date of the agreement between Scalar and the Customer until the end of the next full calendar year. Thereafter, the Measurement Period shall run concurrently with a calendar year. The Uptime is calculated per Measurement Period.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        2.11. Personal Data: personal data as defined in the AVG.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        2.12. Portal: the website or mobile application on which the User can log in to access the Application running on the Server
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        2.13. Server: a computer or associated group of computers and/or other hardware managed by Scalar or third parties engaged by Scalar on which the Application runs
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        2.14. Service Level: an agreed level of service, including Uptime.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        2.15. Access Means: the means permitted by Scalar through which the User accesses the Application on the Server via the Portal.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        2.16. Uptime: the period during the Measurement Period during which the Application is available to the Customer and Users
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        2.17. Working Day: Monday through Friday from 8.30 a.m. to 6 p.m. CET and excluding generally recognised Dutch public holidays.
      </p>
      <p className={`text-[16px] font-[500] text-justify mb-4 ${OpenSans.className}`}>
        3. Scalar Inspect and services related to Scalar Inspect
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        3.1. Scalar grants the Customer the non-exclusive, non-transferable right to use the Application via the Portal during the License Period in accordance with the provisions of these Scalar terms and conditions and any other written or electronically agreed arrangements. Furthermore, the right to use the Application is limited to use by the Customer&apos;s own legal entity and only in the context of providing support to inspectors in recording their inspection findings.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        3.2. Scalar will make the Application available to the Customer as a cloud service by providing access to the Server(s) where the Application is running via a Portal to Users, all this in accordance with the Service Levels and/or agreements on security and/or other agreements laid down in writing or electronically by the parties.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        3.3. Prior to the Customer taking the Application into use, Scalar will provide the Customer with training on the use of the Application, if and to the extent agreed between the parties in writing or electronically.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        3.4. Scalar provides support services, if and to the extent agreed between the parties in writing or electronically.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        3.5. The parties may at any time agree that Scalar will provide additional services related to Scalar Inspect. Such agreements will be recorded in writing or electronically. These additional services are subject to the Scalar terms and conditions.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        3.6. Scalar does not guarantee that the Application made available as a cloud service will always function without errors and without interruptions and/or will be available and/or will be adapted in a timely manner to changes in relevant legislation and regulations.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        3.7. The Customer remains at all times solely responsible and liable for the use of the Application and the Services to support the recording of inspection findings, in particular, but not limited to, determining the inspection findings reported with the support of Scalar’s Application and Services.
      </p>
      <p className={`text-[16px] font-[500] text-justify mb-4 ${OpenSans.className}`}>
        4. Authorization Users
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        4.1. The Customer guarantees that Users will use the Application in accordance with the relevant provisions of these Scalar terms and conditions, including the obligations to keep confidential information confidential and to respect the intellectual property rights of Scalar and/or its licensors.
      </p>
      <p className={`text-[16px] font-[500] text-justify mb-4 ${OpenSans.className}`}>
        5. Updates
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        5.1. Updates to the Application will appear from time to time. Scalar will inform the Customer in a timely manner of any intended update. If the Customer fails to have one or more updates installed in a timely manner, the Customer can no longer invoke the Service Levels agreed by the parties. Scalar will then only be obliged to continue the services to the Customer within reason.
      </p>
      <p className={`text-[16px] font-[500] text-justify mb-4 ${OpenSans.className}`}>
        6. Service Levels and Uptime
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        6.1. The Service Levels and Uptime as agreed upon by the parties in this clause shall apply to the Application.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        6.2. The Application has a Uptime of 99.5%. This Uptime is calculated over the Measurement Period.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        6.3. When calculating the Uptime, the following are excluded:
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        (a) Periods of scheduled maintenance. In case of major maintenance (more than 1 Business Day), Scalar will inform the Customer at least 24 hours before the start of the maintenance work.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        (b) Periods of unplanned maintenance due to the extension of a period of planned maintenance, due to acute problems related to the Application or infrastructure and/or due to an emergency situation.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        (c) Periods during which Scalar cannot provide its Services due to a situation of force majeure, including a situation of force majeure at its suppliers. Force majeure is any circumstance in which a subcontractor of Scalar is unwilling or unable to deliver.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        (d) Periods with outages or disruptions of data transmission from data providers, as long as the outages or disruptions occur outside Scalar&apos;s control;
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        (e) Periods of outages or reduced or disrupted availability, the cause of which is not Scalar, such as failure by the Customer to meet technical requirements and/or problems related to Customer applications and/or problems related to third party services and/or products, such as but not limited to third party networks, connections, bandwidth and/or third party applications or managed by third parties;
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        (f) Periods of downtime or reduced or disrupted availability caused by the Customer&apos;s use of the Application after Scalar has advised the Customer in writing or electronically to change the use, if the Customer has not changed the use as advised;
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        (g) Periods of downtime or reduced or disrupted availability due to external network attacks;
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        (h) Periods of downtime or reduced or disrupted availability due to unauthorized action by Customer or a User or lack of action if required, or otherwise resulting from Customer&apos;s failure to follow appropriate security practices as advised by Scalar;
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        (i) Periods of downtime or reduced or disrupted availability due to incorrect input or instructions in the Application by the Customer and/or User, and/or use in a manner inconsistent with the features and functionality of the Application;
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        (j) Periods during which measures are applied that serve to remedy outages, disruptions and disturbances resulting from outages, disruptions or disturbances as defined above;
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        (k) Other circumstances agreed upon by the parties in the Agreement.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        6.4. Service Levels other than Uptime shall also remain inapplicable if any of the circumstances mentioned in Article 6.3 apply.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        6.5. In the case of Incidents and/or User Questions reported by the Customer, service support will be provided <NAME_EMAIL>. Service support will be provided on Business Days. The Priority (P) of an Incident is determined by Scalar after which the following response and resolution times apply:
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        (a) P1 - Critical: Response time &lt;4 hours and resolution time 1 Business Day.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        (b) P2 - High: Response time &lt;4 hours and resolution time 3 Business Days.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        (c) P3 - Medium: Response time &lt;4 hours and resolution time 20 Working Days.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        (d) P4 - Low: Response time &lt;4 hours and resolution time is 180 Working Days.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        If an Incident is not reported on a Business Day, response times and resolution times will commence on the next Business Day. For User Questions, a response time of 4 hours during Business Days applies.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        6.6. The Uptime and Service Levels do not apply during a period of preview, pre-release, proof of concept or trial versions of the Application.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        6.7. An automatic copy of the data storage and the Application will be made on a daily basis. The retention period of these daily backups is 7 days.
      </p>
      <p className={`text-[16px] font-[500] text-justify mb-4 ${OpenSans.className}`}>
        7. Security
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        7.1. The Application and Client data are stored on Servers located in Microsoft Azure and Google Cloud data centers.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        7.2. Data is encrypted both during in transit (using TLS/SSL) and at rest, using industry-standard encryption protocols such as AES and HTTPS. Scalar employs authentication mechanisms such as (among others) Firebase Authentication and MongoDB&apos;s SCRAM-SHA-256, along with role-based access control to ensure secure access to resources. In addition, Scalar provides protection against common security threats such as Cross-Site Request Forgery (CSRF).
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        7.4. Scalar may impose security rules on Customer in connection with the use of the Application.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        7.5. Scalar is not liable for any damage and/or costs of the Customer caused by a security breach at the Customer.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        7.6 The Customer shall be liable for damages incurred by Scalar as a result of a security breach at the Customer.
      </p>
      <p className={`text-[16px] font-[500] text-justify mb-4 ${OpenSans.className}`}>
        8. Hiring third parties
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        8.1. Scalar is entitled to engage third parties for the provision of its services, without the Customer&apos;s consent. In the event that Scalar, in its capacity as processor within the meaning of the AVG, wishes to engage sub-processors, it will inform the Customer thereof in advance and the Customer will be entitled to object to this, all this in accordance with the provisions of article 28(2) of the AVG.
      </p>
      <p className={`text-[16px] font-[500] text-justify mb-4 ${OpenSans.className}`}>
        9. Technical Requirements Customer
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        9.1. The Customer shall ensure that it has all licenses, hardware, software, and all other technical resources required to access and use the Application. The Client is responsible for obtaining and maintaining an Internet connection with sufficient capacity to be able to use the Application and the Services provided by Scalar. Transmission of the Client data and other information and data over the Internet and/or other telecommunications networks shall be at the sole and exclusive risk and expense of the Client.
      </p>
      <p className={`text-[16px] font-[500] text-justify mb-4 ${OpenSans.className}`}>
        10. Obligations of the Customer
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        10.1. The Customer guarantees that the Access Means will be stored and used with care in order to prevent unauthorized use of and access to the Application. Scalar is not liable for any damage or costs incurred by the Customer or third parties due to unauthorized use of the Access Means.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        10.2. The Customer shall provide Scalar with all reasonable cooperation and all information within the time limits specified by Scalar, if and to the extent Scalar deems it necessary for the performance of the Services.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        10.3. The Customer guarantees that the Client data that the Customer uses, stores or otherwise processes with the Application does not infringe any intellectual property or other rights of Scalar or third parties and does not contain any data or information that is punishable or contrary to morality and/or public order, such as racist, discriminatory or pornographic material.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        10.4. When using the Application, the Customer and/or User will not spread any viruses or other files that could affect the proper functioning of the Application.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        10.5. The Customer and/or User will not send large quantities of unsolicited electronic commercial messages with the same or similar content (spam).
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        10.6 The Customer shall indemnify Scalar against third party claims and demands caused by the Customer&apos;s breach of the Customer&apos;s obligations set out in this clause, and the Customer shall compensate Scalar for all damages and costs caused by the breach.
      </p>
      <p className={`text-[16px] font-[500] text-justify mb-4 ${OpenSans.className}`}>
        11. Support
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        11.1. The Support Desk acts as the point of contact for all Incidents and User Questions relating to the use of the Application. Customer reports Incidents and User Questions via the mail address of the Support Desk: <EMAIL>. If there is an Incident, the Customer must report it to Scalar&apos;s Supportdesk immediately after detection.
      </p>
      <p className={`text-[16px] font-[500] text-justify mb-4 ${OpenSans.className}`}>
        12. Rates for Services
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        12.1. The prices and rates are in euros and exclusive of VAT and other government levies.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        12.2. Scalar is entitled to adjust the prices and rates towards the end of a contract period. Scalar will notify the Customer in writing by email or via the Application at least 60 days prior to the commencement date of any change. The Customer shall then have the right to terminate the Agreement towards the end of a contract period, provided that it is terminated within 30 days after notification of price change.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        12.3. Prices of future functionalities are stated in the Application, the Customer has the choice to use them and pay the additional fees for them.
      </p>
      <p className={`text-[16px] font-[500] text-justify mb-4 ${OpenSans.className}`}>
        13. Invoicing and payment
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        13.1. The Customer must pay invoices within the payment term of 14 days. The Customer is not entitled to suspend any payment or to set off amounts due.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        13.2. In the event of late payment, the Customer shall be in default and shall owe the statutory commercial interest as referred to in article 6:119a of the Dutch Civil Code. In the event of collection of a debt, the extrajudicial collection costs, including all costs of external experts, shall be due in addition to the statutory interest.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        13.3. In the event that the Customer disputes the correctness of an invoice, the Customer will notify Scalar thereof in writing within a period of fourteen (14) days from the invoice date, stating the grounds for the dispute. The right to dispute an invoice lapses.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        13.4. If Scalar does not receive the notification until after the expiry of the aforementioned 14-day period, the Customer shall pay the invoice sent by Scalar in full. The provisions of clause 13.3 also apply in the event that only part of an invoice is disputed and in the event that multiple invoices are disputed.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        13.5. If payment of an invoice has not been made within two (2) months of the invoice date, Scalar is entitled to block access to the Application and suspend the provision of the Services with immediate effect after giving prior warning.
      </p>
      <p className={`text-[16px] font-[500] text-justify mb-4 ${OpenSans.className}`}>
        14. Protection of personal data
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        14.1. If the parties are required to do so under the AVG, they will enter into a processor agreement that meets the requirements of the AVG.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        14.2. The Customer indemnifies Scalar against claims by third parties, including data subjects within the meaning of the AVG, for compensation of damage and costs in connection with any violation by the Customer of the applicable laws and regulations, including in any event the AVG and the AVG Implementation Act, and/or the Scalar terms and conditions and/or agreements and processing agreements entered into between the parties.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        14.3. The Customer shall indemnify Scalar against any fines and/or penalties imposed by the Personal Data Authority (Autoriteit Persoonsgegevens) or any other supervisory authority for any breach of the applicable laws and regulations attributable to the Customer, including in any event the AVG and the AVG Implementation Act, and/or the Scalar terms and conditions and/or any agreements and processing agreements entered into between the parties.
      </p>
      <p className={`text-[16px] font-[500] text-justify mb-4 ${OpenSans.className}`}>
        15. Duration and termination
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        15.1. An agreement enters into force the moment it is signed by both parties.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        15.2. An agreement can be terminated by the Customer towards the end of the contract period by sending an e-<NAME_EMAIL>, provided a notice period of one month is observed. When giving notice of termination, the Customer must state his company name and the reason for termination. Scalar&apos;s service team will then ensure that the exit procedure referred to in Article 16 is initiated.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        15.3 An agreement may be terminated by Scalar in writing or electronically, in whole or in part, towards the end of the contract period, subject to a notice period of one month.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        15.4 An agreement may be terminated by Scalar in writing or electronically with immediate effect, therefore without notice of default, without judicial intervention and without being liable to pay damages and/or costs if:
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        (a) the Customer&apos;s bankruptcy is filed for or the Customer is declared bankrupt;
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        (b) the Customer applies for a moratorium or is granted a (temporary) moratorium;
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        (c) the Customer goes into liquidation or makes a decision to liquidate or is dissolved, other than for the purpose of reorganization or amalgamation of companies;
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        (d) a substantial part of the Customer&apos;s capital is seized, or if the Customer loses the disposal of a substantial part of its capital in any way whatsoever.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        15.5. Either party shall be entitled to dissolve the contract if the other party imputably fails to comply with one or more obligations under the contract and the other party is in default. The right to dissolve exists only if the failure, in view of its nature and significance, justifies dissolution with its consequences. The provisions of Articles 19.1 and 19.2 shall apply in respect of notice of default and default.
      </p>
      <p className={`text-[16px] font-[500] text-justify mb-4 ${OpenSans.className}`}>
        16. Consequences of termination
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        16.1. Amounts that Scalar has invoiced before the termination of the agreement in connection with that which Scalar has already performed or delivered to the Customer in the context of the performance of the agreement will remain payable to Scalar in full. Services and work performed prior to the termination of the agreement for the purposes of implementing the agreement that have not yet been invoiced will still be invoiced by Scalar to the Customer. All invoices shall be immediately due and payable upon termination of the Agreement. This clause 16.1 applies both if the agreement ends by termination or dissolution and by operation of law.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        16.2. If, at the time of dissolution, the Customer has already received performance under the Agreement, such performance and the related payment obligation will not be subject to cancellation. Scalar will never be obliged to refund payments already received from the Customer.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        16.3. Upon termination of the Agreement, the Customer is responsible for the timely export of the Client data. The Customer can retrieve the Client data from the Application by means of the export functionality. The Customer must do this before the end of the Agreement. Scalar is not responsible for the timely export or transfer of Client data. After termination of the Agreement the Client will no longer be able to use the Application and Scalar will block access to the Application and destroy the Client data.
      </p>
      <p className={`text-[16px] font-[500] text-justify mb-4 ${OpenSans.className}`}>
        17. Confidentiality
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        17.1. The parties shall keep confidential any confidential information received from the other party under the Agreement or disclosed to it by the other party, unless a legal obligation or court order requires disclosure of such information.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        17.2. Scalar is entitled to share confidential information with its subcontractors, sub-processors and other third parties it engages for the performance of the Agreement. Scalar will impose an obligation of confidentiality on these third parties.
      </p>
      <p className={`text-[16px] font-[500] text-justify mb-4 ${OpenSans.className}`}>
        18. Intellectual property rights
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        18.1. The intellectual property rights to the Application are vested in Scalar and/or its licensors.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        18.2. The Customer only acquires with respect to the Application the rights as granted to the Customer under these Scalar Terms and Conditions. Nothing in these Scalar Terms and Conditions is intended to transfer to the Customer any intellectual property rights vested in Scalar and/or its licensors.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        18.3. The Customer indemnifies Scalar against claims of third parties with respect to intellectual property rights on software, materials or data provided and/or used by the Customer, including Client data that are used in the performance of the agreement or are processed by Scalar on the instructions of the Customer in the context of the agreement.
      </p>
      <p className={`text-[16px] font-[500] text-justify mb-4 ${OpenSans.className}`}>
        19. Notice of default, default and liability
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        19.1. If one of the parties fails to comply with one or more of its obligations under the contract, the other party shall give it proper notice of default, unless compliance with the obligations in question is already permanently impossible or a fatal deadline, including in any event the payment deadline, has been exceeded, or it must be inferred from a communication from the defaulting party that it will fail to comply with the contract, in which case the defaulting party shall be immediately in default. The notice of default must explain clearly and with reasons the facts and circumstances from which the alleged default consists and which agreed contractual obligations are allegedly breached.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        19.2. Notice of default shall be given in writing by registered letter, whereby the defaulting party shall be granted a reasonable period of time to still fulfill its obligations. If after the expiry of this period the defaulting party still imputably fails to comply with the relevant obligation, it shall be in default in the matter.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        19.3. Any liability of Scalar arising from or related to the performance of an agreement, irrespective of the basis thereof, is limited to the amount paid out in the case in question under the liability insurance(s) taken out by Scalar.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        19.4. If no payment is made under the liability insurance(s) taken out by Scalar in the case in question, for whatever reason, Scalar&apos;s liability, irrespective of the basis thereof, will be limited to an amount of 50% of the amount paid by the Customer to Scalar in the previous calendar year pursuant to the relevant agreement concluded between the parties.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        19.5. Scalar&apos;s liability for consequential damage, loss of profit, lost savings, reduced goodwill, damage due to business interruption, damage as a result of claims by clients of the Customer and for damage as a result of mutilation, destruction, loss or unauthorized use of or access to files, data or documents, including Client data, is excluded.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        19.6. The Customer must report the damage to Scalar in writing as soon as possible and in any event within one month of its occurrence, failing which the right to compensation will lapse.
      </p>
      <p className={`text-[16px] font-[500] text-justify mb-4 ${OpenSans.className}`}>
        20. Force majeure
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        20.1. Neither party will be obliged to comply with any obligation if a party is prevented from doing so as a result of a non-attributable failure (force majeure). Force majeure of Scalar in this context includes force majeure, failure of a supplier, subcontractor or sub-processor of Scalar as a result of which it is unable to comply with the obligations towards Scalar.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        20.2. If the force majeure situation continues for more than 90 days, either party has the right to terminate the relevant agreement by written notice. The work performed by Scalar up to the time of termination of the agreement shall be reimbursed to Scalar by the Customer. The parties shall not otherwise owe each other anything in connection with such termination.
      </p>
      <p className={`text-[16px] font-[500] text-justify mb-4 ${OpenSans.className}`}>
        21. Other provisions
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        21.1. Scalar is entitled to mention in its commercial communications, including its website, that the Customer is a Scalar Customer. Scalar is entitled to use the name and logo of the Customer for this purpose.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        The Customer is not entitled to transfer rights and obligations under an agreement to a third party without the prior written consent of Scalar.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        21.3. Obligations under an agreement which by their nature are intended to continue after the end of the agreement, such as, but not limited to, the article relating to liability, will continue to exist after the end of the agreement.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        21.4. All messages sent by the Customer to Scalar electronically must be addressed to: <EMAIL>.
      </p>
      <p className={`text-[16px] font-[500] text-justify mb-4 ${OpenSans.className}`}>
        22. Amendments and supplements to Scalar terms and conditions.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        22.1 Scalar is entitled to amend and/or supplement the Scalar terms and conditions. Amendments and/or supplements may, for example, be required due to new regulations, technical developments or other developments. Scalar will inform the Customer of any amendments and/or supplements to the Scalar terms and conditions before such amendments and/or supplements become effective.
      </p>
      <p className={`text-[16px] font-[500] text-justify mb-4 ${OpenSans.className}`}>
        23. Applicable law and competent court
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        23.1. Agreements between Scalar and the Customer and the formation thereof are governed by Dutch law.
      </p>
      <p className={`text-[14px] text-justify mb-4 ${OpenSans.className}`}>
        23.2 In case of differences between translations of these terms and conditions, the Dutch translation shall prevail.
      </p>
      <p className={`text-[14px] text-justify ${OpenSans.className}`}>
        23.3. Disputes relating to or arising from agreements concluded between Scalar and the Customer will be submitted in the first instance exclusively to the competent court in Amsterdam.
      </p>
      </div>
    </Modal>
  );
};

export default TNCModal;
