import { db, storage } from "@/firebase.config";
import {
  collection,
  getDocs,
  query,
  where,
  doc,
  updateDoc,
  deleteDoc,
  getDoc,
  arrayRemove,
  writeBatch,
  addDoc,
  arrayUnion,
} from "firebase/firestore";
import axios from "axios";
import { fetch } from "../libs/helpers";
import imageCompression from "browser-image-compression";
import {
  deleteObject,
  getDownloadURL,
  getStorage,
  ref,
  uploadBytes,
} from "firebase/storage";
import { v4 as uuidv4 } from "uuid";
import { createDBFieldObj, mapFiledsWithValue } from "./fieldsMapping";

const host = process.env.NEXT_HOST;

export const fetchAllDocuments = async (inspectionId: any) => {
  try {
    const collectionRef = collection(db, "finding");
    const inspectionRef = doc(db, "inspection", inspectionId);
    const clientId = localStorage.getItem("client");

    const condition = query(
      collectionRef,
      where("inspection", "==", inspectionRef),
      where("client", "==", clientId)
    );

    const querySnapshot = await getDocs(condition);

    const documents = querySnapshot.docs.map((doc: any) => ({
      id: doc.id,
      ...doc.data(),
    }));
    documents.sort((a, b) => a.created_at.seconds - b.created_at.seconds);
    return documents;
  } catch (error) {
    return false;
  }
};

export const deleteFinding = async (documentId: string) => {
  try {
    const docRef = doc(db, "finding", documentId);
    await deleteDoc(docRef);
    return true;
  } catch (e) {
    return false;
  }
};

export const removeImage = async (inspectionDetails: any, documentId: any) => {
  try {
    const docRef = doc(db, "inspection", documentId);
    await updateDoc(docRef, inspectionDetails);
    return true;
  } catch (e) {
    return false;
  }
};

export const exportInspection = async (payload: any) => {
  const idToken = localStorage.getItem("ScalarIdToken");

  const response: any = await fetch({
    url: `${host}/inspections/export`,
    method: "POST",
    data: payload,
  });

  return response;
};

export const CompleteInspections = async (selectedInspections: any) => {
  try {
    const batch = writeBatch(db);

    // Add all operations to the batch
    selectedInspections.forEach((inspectionId: string) => {
      const docRef = doc(db, "inspection", inspectionId);
      batch.update(docRef, { isCompleted: true });
    });

    // Commit the batch
    await batch.commit();
    return true;
  } catch (error) {
    return false;
  }
};

export const TodoInspections = async (selectedInspections: any) => {
  try {
    const batch = writeBatch(db);

    // Add all operations to the batch
    selectedInspections.forEach((inspectionId: string) => {
      const docRef = doc(db, "inspection", inspectionId);
      batch.update(docRef, { isCompleted: false });
    });

    // Commit the batch
    await batch.commit();
    return true;
  } catch (error) {
    return false;
  }
};

export const CompleteOneInspection = async (inspectionId: string) => {
  try {
    // Reference to the specific document
    const docRef = doc(db, "inspection", inspectionId);

    // Update the document
    await updateDoc(docRef, { isCompleted: true });

    return true;
  } catch (error) {
    console.error("Error completing inspection:", error);
    throw error;
  }
};

export const TodoOneInspection = async (inspectionId: string) => {
  try {
    // Reference to the specific document
    const docRef = doc(db, "inspection", inspectionId);

    // Update the document
    await updateDoc(docRef, { isCompleted: false });

    return true;
  } catch (error) {
    console.error("Error marking inspection as to-do:", error);
    throw error;
  }
};

export const deleteInspections = async (
  projectId: string,
  selectedInspections: string[]
): Promise<boolean> => {
  try {
    const projectDocRef = doc(db, "projects", projectId);
    const projectSnapshot = await getDoc(projectDocRef);

    if (!projectSnapshot.exists()) {
      throw new Error("Project not found");
    }

    const batch = writeBatch(db);

    // Add all operations to the batch
    for (const inspectionId of selectedInspections) {
      const inspectionReference = doc(db, "inspection", inspectionId);

      // Add project update to the batch
      batch.update(projectDocRef, {
        inspections: arrayRemove(inspectionReference),
      });

      // Add inspection deletion to the batch
      batch.delete(inspectionReference);
    }

    // Execute all operations atomically
    await batch.commit();

    return true;
  } catch (error) {
    throw error;
  }
};

export const updateFinfingOrder = async (newOrderArray: any) => {
  try {
    const inspectionId: any = localStorage.getItem("ScalarInspectionId");
    const findingRefs = newOrderArray.map((id: string) =>
      doc(db, "finding", id)
    );
    const docRef = doc(db, "inspection", inspectionId);
    await updateDoc(docRef, { finding_order: findingRefs });
    localStorage.setItem("findingOrder", JSON.stringify(newOrderArray));
    return true;
  } catch (e) {
    return false;
  }
};

export const updateFindingOrderForNewDecomp = async (decompId: any) => {
  try {
    const inspectionId: any = localStorage.getItem("ScalarInspectionId");
    const decompRefs = doc(db, "finding", decompId)
    
    const docRef = doc(db, "inspection", inspectionId);
    await updateDoc(docRef, { finding_order: arrayUnion(decompRefs) });

    const inspectionDoc = await getDoc(docRef);
    if (inspectionDoc.exists()) {
      const findingOrder = inspectionDoc.data().finding_order || [];
      const findingOrderIds = findingOrder.map((ref: any) => ref.id ? ref.id : ref.split('/').pop());
      localStorage.setItem("findingOrder", JSON.stringify(findingOrderIds));
    }

    // localStorage.setItem("findingOrder", JSON.stringify(newOrderArray));
    return true;
  } catch (e) {
    return false;
  }
};

const extractFileName = (url: string) => {
  const decodedUrl = decodeURIComponent(url);
  const parts = decodedUrl.split("/");
  const fileNameWithToken = parts[parts.length - 1];
  const fileName = fileNameWithToken.split("?")[0];
  return fileName;
};

const getFilePathFromUrl = (url: string) => {
  try {
    // Handle null or undefined URLs
    if (!url) return null;

    const urlObj = new URL(url);
    const path = urlObj.pathname.split("/o/")[1];
    if (!path) return null;

    return decodeURIComponent(path);
  } catch (error) {
    console.error("Error parsing URL:", url, error);
    return null;
  }
};

export const deleteFilesByUrls = async (urls: string[]) => {
  try {
    const deletePromises = urls.map((url) => {
      const filePath = getFilePathFromUrl(url);
      if (filePath) {
        const fileRef = ref(storage, filePath);
        return deleteObject(fileRef).catch((error) => {
          console.warn(`Failed to delete file: ${filePath}`, error);
        });
      }
      return Promise.resolve();
    });

    await Promise.allSettled(deletePromises);
    return true;
  } catch (error) {
    console.error("Error in deleteFilesByUrls:", error);
    throw error;
  }
};

export const generateUrls = async (files: any) => {
  const urls = [];
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    const storage = getStorage();
    const storageRef = ref(storage, `uploads/finding/${file.name}`);

    try {
      const snapshot = await uploadBytes(storageRef, file);
      const downloadURL = await getDownloadURL(snapshot.ref);

      urls.push(downloadURL);
    } catch (error) { }
  }
  return urls;
};

export const generateFindings = async (
  images: any,
  videos: any,
  setLoader: any,
  inspection_id: any,
  findingTypeId: any,
  reportType: any,
  parentFindingId: any
) => {
  try {
    // Compress images
    const compressedImages = await Promise.all(
      images.map(async (image: any) => {
        const options = {
          maxSizeMB: 1,
          maxWidthOrHeight: 512,
          useWebWorker: true,
        };

        try {
          const compressedImage = await imageCompression(image, options);
          return compressedImage;
        } catch (error) {
          throw error;
        }
      })
    );

    // Generate URLs for images and videos
    const imageUrlArray = await generateUrls(compressedImages);
    const videoUrlArray = videos.length > 0 ? await generateUrls(videos) : [];

    // Create media array for localStorage
    const mediaArray = [
      ...imageUrlArray.map((url) => ({ isVideo: false, url })),
      ...videoUrlArray.map((url) => ({ isVideo: true, url })),
    ];

    // Store the media array in localStorage
    // localStorage.setItem("resize_media", JSON.stringify(mediaArray));

    // Prepare request data
    // const parentFindingId = sessionStorage.getItem("parent_finding_id");
    const rowData = {
      image_urls: imageUrlArray,
      ...(videoUrlArray.length > 0 && { video_urls: videoUrlArray }),
      ...(parentFindingId !== "null" && { parent_finding: parentFindingId }),
    };

    // Make API request for suggested values
    const response: any = await fetch({
      url: `${host}/v4/inspection_types/${reportType}/inspections/${inspection_id}/finding_types/${findingTypeId}/suggestions`,
      method: "POST",
      data: rowData,
    });

    return { ...response, resize_media: mediaArray };

    const docRef = doc(db, "inspection", inspection_id);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      const doc = await docSnap.data();
      if (doc) {
        let fieldKey = "";
        if (findingTypeId === process.env.NEXT_DECOMP_ID) {
          fieldKey = "decomp_fields";
        } else if (findingTypeId === process.env.NEXT_FINDING_ID) {
          fieldKey = "finding_fields";
        } else if (findingTypeId === process.env.NEXT_MEASURE_ID) {
          fieldKey = "measure_fields";
        } else {
          throw new Error("Invalid finding type ID");
        }

        const parsedSuggestion = JSON.parse(doc?.[fieldKey]);
        const mappedFields = mapFiledsWithValue(parsedSuggestion?.fields, response?.suggestions?.fields);
        parsedSuggestion.fields = mappedFields;

        return { suggestions: parsedSuggestion, resize_media: mediaArray };
      }
    } else {
      throw new Error("Inspection not found.");
    }

    // const res = multipickerRes
    // console.log('res', res)
    // return { ...response, resize_media: mediaArray };
  } catch (error) {
    console.error("Error in generateFindings:", error);
    throw error;
  }
};

export const fetchTextSuggestions = async (
  inspectionTypeId: any,
  inspectionId: any,
  findingTypeId: any,
  payload: any
) => {
  try {
    const response: any = await fetch({
      url: `${host}/v4/inspection_types/${inspectionTypeId}/inspections/${inspectionId}/finding_types/${findingTypeId}/text_suggestions`,
      method: "POST",
      data: payload,
    });

    return response;

    const docRef = doc(db, "inspection", inspectionId);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      const doc = await docSnap.data();
      if (doc) {
        let fieldKey = "";
        if (findingTypeId === process.env.NEXT_DECOMP_ID) {
          fieldKey = "decomp_fields";
        } else if (findingTypeId === process.env.NEXT_FINDING_ID) {
          fieldKey = "finding_fields";
        } else if (findingTypeId === process.env.NEXT_MEASURE_ID) {
          fieldKey = "measure_fields";
        } else {
          throw new Error("Invalid finding type ID");
        }

        const parsedSuggestion = JSON.parse(doc?.[fieldKey]);
        const mappedFields = mapFiledsWithValue(parsedSuggestion?.fields, response?.suggestions?.fields);
        parsedSuggestion.fields = mappedFields;

        return { suggestions: parsedSuggestion };
      }
    } else {
      throw new Error("Inspection not found.");
    }

    return response;
  } catch (error) {
    return false;
  }
};

export const addFindingDetails = async (
  inspection_id: any,
  findingDetails: any,
  findingTypeId: any
) => {
  // inspection_id
  try {
    const inspectionRef = doc(db, "inspection", inspection_id);
    const findingTypeRef = doc(db, "finding_type", findingTypeId);
    findingDetails.inspection = inspectionRef;
    findingDetails.finding_type = findingTypeRef;
    const finding = await addDoc(collection(db, "finding"), findingDetails);
    if (finding.id) {
      return finding;
    } else {
      return false;
    }
  } catch (e) {
    console.error("error", e);
    return false;
  }
};

export const addFindingDetailsWithMedia = async (
  inspection_id: any,
  findingDetails: any,
  findingTypeId: any,
  resizeMedia: any,
  imagesArray: any
) => {
  // inspection_id
  try {

    // const dbFields = createDBFieldObj(JSON.parse(findingDetails?.fields))
    // findingDetails.fields = dbFields

    const inspectionRef = doc(db, "inspection", inspection_id);
    const findingTypeRef = doc(db, "finding_type", findingTypeId);
    findingDetails.inspection = inspectionRef;
    findingDetails.finding_type = findingTypeRef;

    const videosToUse = resizeMedia
      .filter((media: any) => media.isVideo)
      .map((media: any) => {
        return {
          ...media,
          resize_url: media.url,
        };
      });

    const compressedImages = await Promise.all(
      imagesArray.map(async (image: any) => {
        const options = {
          maxWidthOrHeight: 1280,
          useWebWorker: true,
        };
        const compressedImage = await imageCompression(image, options);
        return compressedImage;
      })
    );

    const files = [...compressedImages];
    const medias = await handleUpload(files, resizeMedia);

    const allMedia = [...medias, ...videosToUse];

    findingDetails.media = allMedia;

    const finding = await addDoc(collection(db, "finding"), findingDetails);
    if (finding.id) {
      return finding;
    } else {
      return false;
    }
  } catch (e) {
    console.error("error", e);
    return false;
  }
};

export const handleUpload = async (files: any, mediaArray: any) => {
  const urls = [];
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    const mediaUrlObj = mediaArray.find((media: any) => {
      const mediaFileName = extractFileName(media.url);
      return mediaFileName === file.name;
    });
    const fileExtension = file.name.split(".").pop();

    // Generate new filename based on type and timestamp
    const newFileName = file.type.startsWith("video/")
      ? `video_${uuidv4()}.${fileExtension}`
      : `image_${uuidv4()}.${fileExtension}`;

    const storage = getStorage();
    const storageRef = ref(storage, `uploads/finding/${newFileName}`);

    try {
      const snapshot = await uploadBytes(storageRef, file);
      const downloadURL = await getDownloadURL(snapshot.ref);

      if (file.type.startsWith("video/")) {
        urls.push({
          isVideo: true,
          url: downloadURL,
          resize_url: mediaUrlObj.url,
        });
      } else {
        urls.push({
          isVideo: false,
          url: downloadURL,
          resize_url: mediaUrlObj.url,
        });
      }
    } catch (error) { }
  }
  return urls;
};

export const updateFindingDetails = async (
  findingDetails: any,
  documentId: string
) => {
  try {
    const docRef = doc(db, "finding", documentId);
    await updateDoc(docRef, findingDetails);
    // Get the updated document after update
    const updatedDoc = await getDoc(docRef);
    if (updatedDoc.exists()) {
      return {
        id: updatedDoc.id,
        ...updatedDoc.data(),
      };
    }
    return true;
  } catch (e) {
    return false;
  }
};

export const fetchAPIResponse = async (
  inspectionType: any,
  inspection_id: any,
  findingTypeId: any,
  payload: any,
  fieldId: any
) => {
  try {
    const response: any = await fetch({
      url: `${host}/v4/inspection_types/${inspectionType}/inspections/${inspection_id}/finding_types/${findingTypeId}/api_fields/${fieldId}/calculate`,
      method: "POST",
      data: payload,
    });

    return response;
  } catch (error) {
    return false;
  }
};

export const getMeasures = async (findingId: any) => {
  const collectionRef = collection(db, "finding");
  const clientId = localStorage.getItem("client");

  const condition = query(
    collectionRef,
    where("parent_finding_id", "==", findingId),
    where("client", "==", clientId)
  );

  const querySnapshot = await getDocs(condition);

  const documents = querySnapshot.docs.map((doc: any) => ({
    id: doc.id,
    ...doc.data(),
  }));
  documents.sort((a, b) => a.created_at.seconds - b.created_at.seconds);
  return documents;
}
