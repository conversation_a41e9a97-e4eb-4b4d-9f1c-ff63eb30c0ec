'use client';

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Open_Sans } from 'next/font/google';
import { Button, Modal as AntdModal, message, Checkbox } from 'antd';
import {
  exportInspection,
  CompleteInspections,
  TodoInspections,
} from '@/src/services/newInspection.api';

const OpenSans = Open_Sans({ weight: '400', subsets: ['latin'] });

interface ExportModalProps {
  setIsModalOpen: (value: boolean) => void;
  selectedInspections: string[];
  setRefresh: (cb: (prev: any) => any) => void;
  setSearchedInspections: (data: any[]) => void;
  searchedInspections: any[];
  isModalOpen: boolean;
  setMenu: (val: boolean) => void;
}

const ExportModal: React.FC<ExportModalProps> = ({
  setIsModalOpen,
  selectedInspections,
  setRefresh,
  setSearchedInspections,
  searchedInspections,
  isModalOpen,
  setMenu,
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [complete, setComplete] = useState(false);
  const [exportInsp, setExportInsp] = useState(false);

  const exportInspections = async () => {
    const payload = {
      user_id: localStorage.getItem('ScalarUserId'),
      inspection_ids: selectedInspections,
    };

    const res = await exportInspection(payload);
    return res?.inspection_ids?.length === selectedInspections.length;
  };

  const updateInspectionStates = (isCompleted: boolean) => {
    const updated = searchedInspections.map((insp: any) =>
      selectedInspections.includes(insp.id)
        ? { ...insp, isCompleted }
        : insp
    );
    setSearchedInspections(updated);
  };

  const markCompleteInspections = async () => {
    const res = await CompleteInspections(selectedInspections);
    if (res) updateInspectionStates(true);
    return res;
  };

  const markTodoInspections = async () => {
    const res = await TodoInspections(selectedInspections);
    if (res) updateInspectionStates(false);
    return res;
  };

  const handleExport = async () => {
    try {
      setLoading(true);

      if (selectedInspections.length === 0) {
        message.warning(t('Please select inspections to continue.'));
        return;
      }

      const tasks = [];

      if (complete) tasks.push(markCompleteInspections());
      else tasks.push(markTodoInspections());

      if (exportInsp) tasks.push(exportInspections());

      const results = await Promise.all(tasks);

      if (results.includes(false)) {
        message.warning(t('Some operations failed.'));
      } else {
        message.success(t('Successfully completed the export process.'));
      }

      setRefresh((prev: any) => !prev);
      setIsModalOpen(false);
    } catch (error) {
      message.error(t('Something went wrong, try again later.'));
    } finally {
      setLoading(false);
      setMenu(false);
    }
  };

  return (
    <AntdModal
      open={isModalOpen}
      onCancel={() => setIsModalOpen(false)}
      footer={null}
      centered
      className={`${OpenSans.className} custom-export-modal`}
      width={350}
    >
      <div className="relative">

        <div className="mt-6">
          <h1 className="text-left text-[25px] leading-[52.08px] font-[600]">
            {t('Export')} {t('Inspections')}!
          </h1>
          <p className="text-[18px] text-left mt-2">
            {t('Are you sure you want to Export')} <br />
            {t('Inspections')}?
          </p>

          <div className="flex flex-col items-start gap-2 mt-4">
            <Checkbox onChange={(e) => setComplete(e.target.checked)}>
              {t('Mark inspections as completed')}
            </Checkbox>
            <Checkbox onChange={(e) => setExportInsp(e.target.checked)}>
              {t('Export inspections')}
            </Checkbox>
          </div>

          <div className="text-center flex justify-between gap-4 mt-8">
            <Button
              type="default"
              className="w-[50%] h-[45px] text-[14px] text-[#FF9200] border border-[#FF9200] bg-[#ff910020] hover:bg-[#ff910015]"
              onClick={() => setIsModalOpen(false)}
            >
              {t('Cancel')}
            </Button>
            <Button
              type="primary"
              className="w-[50%] h-[45px] text-[14px] border-none"
              style={{
                backgroundColor: '#FF9200',
                color: 'white',
              }}
              loading={loading}
              onClick={handleExport}
            >
              {t('Export')}
            </Button>
          </div>
        </div>
      </div>
    </AntdModal>
  );
};

export default ExportModal;
