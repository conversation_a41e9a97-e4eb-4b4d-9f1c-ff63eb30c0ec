"use client";

import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Open_Sans } from "next/font/google";
import { useRouter } from "next/navigation";
import {
  exportInspection,
  CompleteOneInspection,
  TodoOneInspection,
} from "@/src/services/newInspection.api";
import { Modal, Button, Checkbox, message } from "antd";

const OpenSans = Open_Sans({ weight: "400", subsets: ["latin"] });

interface ExportModalProps {
  setIsModalOpen: (val: boolean) => void;
  isModalOpen: boolean;
}

const ExportModal: React.FC<ExportModalProps> = ({
  setIsModalOpen,
  isModalOpen,
}) => {
  const { t } = useTranslation();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [complete, setComplete] = useState(false);
  const [exportInsp, setExportInsp] = useState(false);

  const inspectionExport = async (id: string) => {
    try {
      const payload = {
        user_id: localStorage.getItem("ScalarUserId"),
        inspection_ids: [id],
      };

      const res = await exportInspection(payload);
      if (res.inspection_ids.includes(id)) {
        return true;
      }
    } catch (error) {
      // message.error(
      //   t("Something went wrong while exporting, try again later.")
      // );
      return false;
    }
  };

  const markCompleteInspection = async (id: string) => {
    try {
      const res = await CompleteOneInspection(id);
      return !!res;
    } catch (error) {
      // message.error(t("Something went wrong, try again later!"));
      return false;
    }
  };

  const markTodoInspection = async (id: string) => {
    try {
      const res = await TodoOneInspection(id);
      return !!res;
    } catch (error) {
      // message.error(t("Something went wrong, try again later!"));
      return false;
    }
  };

  const handleExport = async () => {
    const id: any = localStorage.getItem("ScalarInspectionId");
    setLoading(true);

    if (exportInsp && complete) {
      markCompleteInspection(id);
      inspectionExport(id);
    } else if (complete && !exportInsp) {
      markCompleteInspection(id);
    } else if (!complete && exportInsp) {
      markTodoInspection(id);
      inspectionExport(id);
    } else {
      markTodoInspection(id);
    }

    router.push("/project")
    setLoading(false);
    setIsModalOpen(false);
  };

  return (
    <Modal
      open={isModalOpen}
      title={
        <h1 className=" text-left text-[25px] leading-[52.08px] font-[600]">
          {t("Export Inspection")}!
        </h1>
      }
      onCancel={() => setIsModalOpen(false)}
      footer={null}
      centered
      className={`${OpenSans.className} custom-export-modal`}
      width={400}
    >
      <div className="relative">
        <p className="text-[18px] text-left">
          {t("Are you sure you want to Export")} <br />
          {t("Inspection")}?
        </p>

        <div className="flex flex-col items-start gap-1 mt-2">
          <Checkbox onChange={(e) => setComplete(e.target.checked)}>
            {t("Mark inspection as completed")}
          </Checkbox>
          <Checkbox onChange={(e) => setExportInsp(e.target.checked)}>
            {t("Export inspection")}
          </Checkbox>
        </div>

        <div className="text-center flex justify-between gap-4 mt-6">
          <button
            type="button"
            className="w-[50%] h-[45px] text-[14px] text-[#FF9200] border border-[#FF9200] rounded-lg bg-[#ff910020] hover:bg-[#ff910015]"
            onClick={() => setIsModalOpen(false)}
          >
            {t("Cancel")}
          </button>
          <Button
            className="w-[50%] h-[45px] text-[14px] text-white border border-[#FF9200] rounded-lg bg-[#FF9200] hover:bg-[#ff9100d0] transition-all"
            style={{
              backgroundColor: "#FF9200",
              color: "white",
              borderColor: "transparent",
            }}
            onClick={handleExport}
            loading={loading}
          >
            {t("Export")}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default ExportModal;
